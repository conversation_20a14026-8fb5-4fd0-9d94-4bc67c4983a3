<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域联动调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        select {
            width: 200px;
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>区域联动调试页面</h1>
        
        <div class="test-section">
            <h3>1. API接口测试</h3>
            <button onclick="testProvincesAPI()">测试省份API</button>
            <button onclick="testCitiesAPI()">测试城市API (广东省)</button>
            <button onclick="testDistrictsAPI()">测试区县API (广州市)</button>
            <div id="apiResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 区域选择器测试</h3>
            <div>
                <select id="testProvince">
                    <option value="">请选择省份</option>
                </select>
                <select id="testCity">
                    <option value="">请选择城市</option>
                </select>
                <select id="testDistrict">
                    <option value="">请选择区县</option>
                </select>
            </div>
            <button onclick="initRegionSelector()">初始化区域选择器</button>
            <button onclick="getSelectedRegion()">获取选中区域</button>
            <div id="selectorResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 控制台日志</h3>
            <button onclick="clearConsole()">清空控制台</button>
            <div id="consoleLog" class="result"></div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/region-selector.js') }}"></script>
    <script>
        let regionSelector;
        
        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleLogDiv = document.getElementById('consoleLog');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleLogDiv.textContent += '[LOG] ' + message + '\n';
            consoleLogDiv.scrollTop = consoleLogDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleLogDiv.textContent += '[ERROR] ' + message + '\n';
            consoleLogDiv.scrollTop = consoleLogDiv.scrollHeight;
        };
        
        function clearConsole() {
            consoleLogDiv.textContent = '';
        }
        
        async function testProvincesAPI() {
            const resultDiv = document.getElementById('apiResult');
            try {
                resultDiv.textContent = '正在请求省份数据...';
                const response = await fetch('/api/regions/provinces');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `省份数据获取成功 (${data.length}个省份):\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${response.status} ${response.statusText}\n` + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求异常: ${error.message}`;
            }
        }
        
        async function testCitiesAPI() {
            const resultDiv = document.getElementById('apiResult');
            try {
                resultDiv.textContent = '正在请求城市数据...';
                const response = await fetch('/api/regions/cities/440000');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `城市数据获取成功 (${data.length}个城市):\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${response.status} ${response.statusText}\n` + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求异常: ${error.message}`;
            }
        }
        
        async function testDistrictsAPI() {
            const resultDiv = document.getElementById('apiResult');
            try {
                resultDiv.textContent = '正在请求区县数据...';
                const response = await fetch('/api/regions/districts/440100');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `区县数据获取成功 (${data.length}个区县):\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${response.status} ${response.statusText}\n` + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求异常: ${error.message}`;
            }
        }
        
        function initRegionSelector() {
            const resultDiv = document.getElementById('selectorResult');
            try {
                regionSelector = new RegionSelector({
                    provinceSelector: '#testProvince',
                    citySelector: '#testCity',
                    districtSelector: '#testDistrict',
                    provincePlaceholder: '请选择省份',
                    cityPlaceholder: '请选择城市',
                    districtPlaceholder: '请选择区县',
                    onChange: function(region) {
                        console.log('区域选择变化:', region);
                        resultDiv.textContent = '区域选择变化:\n' + JSON.stringify(region, null, 2);
                    }
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '区域选择器初始化成功';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `初始化失败: ${error.message}`;
                console.error('初始化区域选择器失败:', error);
            }
        }
        
        function getSelectedRegion() {
            const resultDiv = document.getElementById('selectorResult');
            if (!regionSelector) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请先初始化区域选择器';
                return;
            }
            
            try {
                const region = regionSelector.getSelectedRegion();
                resultDiv.className = 'result success';
                resultDiv.textContent = '当前选中区域:\n' + JSON.stringify(region, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${error.message}`;
            }
        }
        
        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始调试...');
            initRegionSelector();
        });
    </script>
</body>
</html>
