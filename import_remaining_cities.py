#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入剩余省份的城市数据
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

def import_remaining_cities():
    """导入剩余省份的城市数据"""
    app = create_app()
    with app.app_context():
        
        # 剩余省份的城市数据
        remaining_cities = [
            # 广西壮族自治区
            {'id': '450100', 'name': '南宁市', 'parent_id': '450000'},
            {'id': '450200', 'name': '柳州市', 'parent_id': '450000'},
            {'id': '450300', 'name': '桂林市', 'parent_id': '450000'},
            {'id': '450400', 'name': '梧州市', 'parent_id': '450000'},
            {'id': '450500', 'name': '北海市', 'parent_id': '450000'},
            {'id': '450600', 'name': '防城港市', 'parent_id': '450000'},
            {'id': '450700', 'name': '钦州市', 'parent_id': '450000'},
            {'id': '450800', 'name': '贵港市', 'parent_id': '450000'},
            {'id': '450900', 'name': '玉林市', 'parent_id': '450000'},
            {'id': '451000', 'name': '百色市', 'parent_id': '450000'},
            {'id': '451100', 'name': '贺州市', 'parent_id': '450000'},
            {'id': '451200', 'name': '河池市', 'parent_id': '450000'},
            {'id': '451300', 'name': '来宾市', 'parent_id': '450000'},
            {'id': '451400', 'name': '崇左市', 'parent_id': '450000'},
            
            # 海南省
            {'id': '460100', 'name': '海口市', 'parent_id': '460000'},
            {'id': '460200', 'name': '三亚市', 'parent_id': '460000'},
            {'id': '460300', 'name': '三沙市', 'parent_id': '460000'},
            {'id': '460400', 'name': '儋州市', 'parent_id': '460000'},
            
            # 重庆市（已在主脚本中添加为直辖市）
            
            # 四川省
            {'id': '510100', 'name': '成都市', 'parent_id': '510000'},
            {'id': '510300', 'name': '自贡市', 'parent_id': '510000'},
            {'id': '510400', 'name': '攀枝花市', 'parent_id': '510000'},
            {'id': '510500', 'name': '泸州市', 'parent_id': '510000'},
            {'id': '510600', 'name': '德阳市', 'parent_id': '510000'},
            {'id': '510700', 'name': '绵阳市', 'parent_id': '510000'},
            {'id': '510800', 'name': '广元市', 'parent_id': '510000'},
            {'id': '510900', 'name': '遂宁市', 'parent_id': '510000'},
            {'id': '511000', 'name': '内江市', 'parent_id': '510000'},
            {'id': '511100', 'name': '乐山市', 'parent_id': '510000'},
            {'id': '511300', 'name': '南充市', 'parent_id': '510000'},
            {'id': '511400', 'name': '眉山市', 'parent_id': '510000'},
            {'id': '511500', 'name': '宜宾市', 'parent_id': '510000'},
            {'id': '511600', 'name': '广安市', 'parent_id': '510000'},
            {'id': '511700', 'name': '达州市', 'parent_id': '510000'},
            {'id': '511800', 'name': '雅安市', 'parent_id': '510000'},
            {'id': '511900', 'name': '巴中市', 'parent_id': '510000'},
            {'id': '512000', 'name': '资阳市', 'parent_id': '510000'},
            {'id': '513200', 'name': '阿坝藏族羌族自治州', 'parent_id': '510000'},
            {'id': '513300', 'name': '甘孜藏族自治州', 'parent_id': '510000'},
            {'id': '513400', 'name': '凉山彝族自治州', 'parent_id': '510000'},
            
            # 贵州省
            {'id': '520100', 'name': '贵阳市', 'parent_id': '520000'},
            {'id': '520200', 'name': '六盘水市', 'parent_id': '520000'},
            {'id': '520300', 'name': '遵义市', 'parent_id': '520000'},
            {'id': '520400', 'name': '安顺市', 'parent_id': '520000'},
            {'id': '520500', 'name': '毕节市', 'parent_id': '520000'},
            {'id': '520600', 'name': '铜仁市', 'parent_id': '520000'},
            {'id': '522300', 'name': '黔西南布依族苗族自治州', 'parent_id': '520000'},
            {'id': '522600', 'name': '黔东南苗族侗族自治州', 'parent_id': '520000'},
            {'id': '522700', 'name': '黔南布依族苗族自治州', 'parent_id': '520000'},
            
            # 云南省
            {'id': '530100', 'name': '昆明市', 'parent_id': '530000'},
            {'id': '530300', 'name': '曲靖市', 'parent_id': '530000'},
            {'id': '530400', 'name': '玉溪市', 'parent_id': '530000'},
            {'id': '530500', 'name': '保山市', 'parent_id': '530000'},
            {'id': '530600', 'name': '昭通市', 'parent_id': '530000'},
            {'id': '530700', 'name': '丽江市', 'parent_id': '530000'},
            {'id': '530800', 'name': '普洱市', 'parent_id': '530000'},
            {'id': '530900', 'name': '临沧市', 'parent_id': '530000'},
            {'id': '532300', 'name': '楚雄彝族自治州', 'parent_id': '530000'},
            {'id': '532500', 'name': '红河哈尼族彝族自治州', 'parent_id': '530000'},
            {'id': '532600', 'name': '文山壮族苗族自治州', 'parent_id': '530000'},
            {'id': '532800', 'name': '西双版纳傣族自治州', 'parent_id': '530000'},
            {'id': '532900', 'name': '大理白族自治州', 'parent_id': '530000'},
            {'id': '533100', 'name': '德宏傣族景颇族自治州', 'parent_id': '530000'},
            {'id': '533300', 'name': '怒江傈僳族自治州', 'parent_id': '530000'},
            {'id': '533400', 'name': '迪庆藏族自治州', 'parent_id': '530000'},
            
            # 西藏自治区
            {'id': '540100', 'name': '拉萨市', 'parent_id': '540000'},
            {'id': '540200', 'name': '日喀则市', 'parent_id': '540000'},
            {'id': '540300', 'name': '昌都市', 'parent_id': '540000'},
            {'id': '540400', 'name': '林芝市', 'parent_id': '540000'},
            {'id': '540500', 'name': '山南市', 'parent_id': '540000'},
            {'id': '540600', 'name': '那曲市', 'parent_id': '540000'},
            {'id': '542500', 'name': '阿里地区', 'parent_id': '540000'},
            
            # 陕西省
            {'id': '610100', 'name': '西安市', 'parent_id': '610000'},
            {'id': '610200', 'name': '铜川市', 'parent_id': '610000'},
            {'id': '610300', 'name': '宝鸡市', 'parent_id': '610000'},
            {'id': '610400', 'name': '咸阳市', 'parent_id': '610000'},
            {'id': '610500', 'name': '渭南市', 'parent_id': '610000'},
            {'id': '610600', 'name': '延安市', 'parent_id': '610000'},
            {'id': '610700', 'name': '汉中市', 'parent_id': '610000'},
            {'id': '610800', 'name': '榆林市', 'parent_id': '610000'},
            {'id': '610900', 'name': '安康市', 'parent_id': '610000'},
            {'id': '611000', 'name': '商洛市', 'parent_id': '610000'},
            
            # 甘肃省
            {'id': '620100', 'name': '兰州市', 'parent_id': '620000'},
            {'id': '620200', 'name': '嘉峪关市', 'parent_id': '620000'},
            {'id': '620300', 'name': '金昌市', 'parent_id': '620000'},
            {'id': '620400', 'name': '白银市', 'parent_id': '620000'},
            {'id': '620500', 'name': '天水市', 'parent_id': '620000'},
            {'id': '620600', 'name': '武威市', 'parent_id': '620000'},
            {'id': '620700', 'name': '张掖市', 'parent_id': '620000'},
            {'id': '620800', 'name': '平凉市', 'parent_id': '620000'},
            {'id': '620900', 'name': '酒泉市', 'parent_id': '620000'},
            {'id': '621000', 'name': '庆阳市', 'parent_id': '620000'},
            {'id': '621100', 'name': '定西市', 'parent_id': '620000'},
            {'id': '621200', 'name': '陇南市', 'parent_id': '620000'},
            {'id': '622900', 'name': '临夏回族自治州', 'parent_id': '620000'},
            {'id': '623000', 'name': '甘南藏族自治州', 'parent_id': '620000'},
            
            # 青海省
            {'id': '630100', 'name': '西宁市', 'parent_id': '630000'},
            {'id': '630200', 'name': '海东市', 'parent_id': '630000'},
            {'id': '632200', 'name': '海北藏族自治州', 'parent_id': '630000'},
            {'id': '632300', 'name': '黄南藏族自治州', 'parent_id': '630000'},
            {'id': '632500', 'name': '海南藏族自治州', 'parent_id': '630000'},
            {'id': '632600', 'name': '果洛藏族自治州', 'parent_id': '630000'},
            {'id': '632700', 'name': '玉树藏族自治州', 'parent_id': '630000'},
            {'id': '632800', 'name': '海西蒙古族藏族自治州', 'parent_id': '630000'},
            
            # 宁夏回族自治区
            {'id': '640100', 'name': '银川市', 'parent_id': '640000'},
            {'id': '640200', 'name': '石嘴山市', 'parent_id': '640000'},
            {'id': '640300', 'name': '吴忠市', 'parent_id': '640000'},
            {'id': '640400', 'name': '固原市', 'parent_id': '640000'},
            {'id': '640500', 'name': '中卫市', 'parent_id': '640000'},
            
            # 新疆维吾尔自治区
            {'id': '650100', 'name': '乌鲁木齐市', 'parent_id': '650000'},
            {'id': '650200', 'name': '克拉玛依市', 'parent_id': '650000'},
            {'id': '650400', 'name': '吐鲁番市', 'parent_id': '650000'},
            {'id': '650500', 'name': '哈密市', 'parent_id': '650000'},
            {'id': '652300', 'name': '昌吉回族自治州', 'parent_id': '650000'},
            {'id': '652700', 'name': '博尔塔拉蒙古自治州', 'parent_id': '650000'},
            {'id': '652800', 'name': '巴音郭楞蒙古自治州', 'parent_id': '650000'},
            {'id': '652900', 'name': '阿克苏地区', 'parent_id': '650000'},
            {'id': '653000', 'name': '克孜勒苏柯尔克孜自治州', 'parent_id': '650000'},
            {'id': '653100', 'name': '喀什地区', 'parent_id': '650000'},
            {'id': '653200', 'name': '和田地区', 'parent_id': '650000'},
            {'id': '654000', 'name': '伊犁哈萨克自治州', 'parent_id': '650000'},
            {'id': '654200', 'name': '塔城地区', 'parent_id': '650000'},
            {'id': '654300', 'name': '阿勒泰地区', 'parent_id': '650000'},
            
            # 台湾省（部分主要城市）
            {'id': '710100', 'name': '台北市', 'parent_id': '710000'},
            {'id': '710200', 'name': '高雄市', 'parent_id': '710000'},
            {'id': '710300', 'name': '台中市', 'parent_id': '710000'},
            {'id': '710400', 'name': '台南市', 'parent_id': '710000'},
            {'id': '710500', 'name': '新北市', 'parent_id': '710000'},
            {'id': '710600', 'name': '桃园市', 'parent_id': '710000'},
            
            # 香港特别行政区
            {'id': '810100', 'name': '香港岛', 'parent_id': '810000'},
            {'id': '810200', 'name': '九龙', 'parent_id': '810000'},
            {'id': '810300', 'name': '新界', 'parent_id': '810000'},
            
            # 澳门特别行政区
            {'id': '820100', 'name': '澳门半岛', 'parent_id': '820000'},
            {'id': '820200', 'name': '氹仔', 'parent_id': '820000'},
            {'id': '820300', 'name': '路环', 'parent_id': '820000'},
        ]
        
        try:
            # 导入剩余城市数据
            print(f"正在导入剩余 {len(remaining_cities)} 个城市...")
            for idx, city in enumerate(remaining_cities):
                region = Region(
                    id=city['id'],
                    name=city['name'],
                    parent_id=city['parent_id'],
                    level=2,
                    sort_order=idx + 1000  # 避免与现有数据冲突
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功导入 {len(remaining_cities)} 个城市")
            
            # 统计总数
            total_provinces = Region.query.filter_by(level=1).count()
            total_cities = Region.query.filter_by(level=2).count()
            total_districts = Region.query.filter_by(level=3).count()
            
            print(f"\n=== 全国区域数据统计 ===")
            print(f"省份数量: {total_provinces}")
            print(f"城市数量: {total_cities}")
            print(f"区县数量: {total_districts}")
            print(f"总计: {total_provinces + total_cities + total_districts} 条记录")
            
        except Exception as e:
            db.session.rollback()
            print(f"导入数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    import_remaining_cities()
