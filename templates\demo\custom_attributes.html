{% extends "base_modern.html" %}

{% block title %}自定义属性演示 - {{ site_config.site_name }}{% endblock %}

{% block extra_css %}
<style>
    .demo-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: var(--border-radius-2xl);
        box-shadow: var(--shadow-xl);
    }
    
    .demo-title {
        text-align: center;
        margin-bottom: 3rem;
        color: var(--gray-900);
    }
    
    .demo-section {
        margin-bottom: 3rem;
        padding: 2rem;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius-xl);
        background: white;
    }
    
    .demo-section h3 {
        color: var(--primary-600);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .attribute-demo {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: start;
    }
    
    .attribute-group {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        background: var(--gradient-light);
    }
    
    .attribute-group h4 {
        margin-bottom: 1rem;
        color: var(--gray-800);
        font-size: 1.1rem;
        font-weight: 600;
    }
    
    /* 固定选项样式 */
    .fixed-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .option-btn {
        padding: 0.5rem 1rem;
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-md);
        background: white;
        color: var(--gray-700);
        cursor: pointer;
        transition: var(--transition-base);
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .option-btn:hover {
        border-color: var(--primary-400);
        background: rgba(102, 126, 234, 0.05);
    }
    
    .option-btn.selected {
        border-color: var(--primary-500);
        background: var(--primary-500);
        color: white;
    }
    
    /* 自定义输入样式 */
    .custom-input-group {
        margin-bottom: 1rem;
    }
    
    .custom-input-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--gray-700);
    }
    
    .custom-input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--gray-300);
        border-radius: var(--border-radius-md);
        font-size: 0.9rem;
        transition: var(--transition-base);
    }
    
    .custom-input:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .custom-input.valid {
        border-color: var(--success-500);
        background: rgba(16, 185, 129, 0.05);
    }
    
    .custom-input.invalid {
        border-color: var(--danger-500);
        background: rgba(239, 68, 68, 0.05);
    }
    
    .input-feedback {
        margin-top: 0.25rem;
        font-size: 0.8rem;
    }
    
    .input-feedback.valid {
        color: var(--success-600);
    }
    
    .input-feedback.invalid {
        color: var(--danger-600);
    }
    
    /* 价格显示 */
    .price-display {
        background: var(--gradient-light);
        padding: 2rem;
        border-radius: var(--border-radius-xl);
        text-align: center;
        border: 1px solid var(--primary-200);
    }
    
    .price-label {
        font-size: 1rem;
        color: var(--gray-600);
        margin-bottom: 0.5rem;
    }
    
    .price-value {
        font-size: 2.5rem;
        font-weight: 800;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }
    
    .price-breakdown {
        font-size: 0.9rem;
        color: var(--gray-600);
        text-align: left;
    }
    
    .price-breakdown div {
        margin-bottom: 0.25rem;
    }
    
    .demo-actions {
        text-align: center;
        margin-top: 2rem;
    }
    
    .demo-btn {
        padding: 0.75rem 2rem;
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-base);
        margin: 0 0.5rem;
    }
    
    .demo-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .demo-btn.secondary {
        background: var(--gray-500);
    }
</style>
{% endblock %}

{% block content %}
<div class="demo-container">
    <h1 class="demo-title">
        <i class="fas fa-cogs me-3"></i>
        自定义属性功能演示
    </h1>
    
    <!-- 功能说明 -->
    <div class="demo-section">
        <h3><i class="fas fa-info-circle"></i> 功能说明</h3>
        <p>本演示展示了如何在商品中同时使用<strong>固定选项属性</strong>和<strong>自定义输入属性</strong>：</p>
        <ul>
            <li><strong>固定选项</strong>：预设的选择项，如尺寸、纸张类型等</li>
            <li><strong>自定义输入</strong>：用户可以输入自定义值，如每本张数、特殊要求等</li>
            <li><strong>实时价格计算</strong>：根据选择的属性和输入的数量实时计算价格</li>
        </ul>
    </div>
    
    <!-- 属性选择演示 -->
    <div class="demo-section">
        <h3><i class="fas fa-sliders-h"></i> 属性选择演示</h3>
        
        <div class="attribute-demo">
            <!-- 左侧：属性选择 -->
            <div>
                <!-- 尺寸选择（固定选项） -->
                <div class="attribute-group">
                    <h4>📏 尺寸</h4>
                    <div class="fixed-options">
                        <div class="option-btn" data-group="size" data-value="大16开" data-price="0">大16开 (210*285)</div>
                        <div class="option-btn" data-group="size" data-value="大32开" data-price="-2">大32开 (148*210)</div>
                        <div class="option-btn" data-group="size" data-value="A4" data-price="1.5">A4 (210*297)</div>
                        <div class="option-btn" data-group="size" data-value="A5" data-price="-1">A5 (148*210)</div>
                    </div>
                </div>
                
                <!-- 纸张选择（固定选项） -->
                <div class="attribute-group">
                    <h4>📄 纸张</h4>
                    <div class="fixed-options">
                        <div class="option-btn" data-group="paper" data-value="白红" data-price="0">白红</div>
                        <div class="option-btn" data-group="paper" data-value="牛皮纸" data-price="2.5">牛皮纸 (+¥2.5)</div>
                        <div class="option-btn" data-group="paper" data-value="瓦楞纸" data-price="3">瓦楞纸 (+¥3.0)</div>
                    </div>
                </div>
                
                <!-- 每本张数（自定义输入） -->
                <div class="attribute-group">
                    <h4>📖 每本张数</h4>
                    <div class="custom-input-group">
                        <label for="pages">请输入每本张数：</label>
                        <input type="number" 
                               id="pages" 
                               class="custom-input" 
                               placeholder="请输入每本张数" 
                               min="1" 
                               max="9999" 
                               value="100"
                               data-group="pages"
                               data-formula="quantity * 0.24">
                        <div class="input-feedback" id="pages-feedback"></div>
                        <small class="text-muted">
                            <i class="fas fa-calculator me-1"></i>
                            价格计算公式：张数 × 0.24元
                        </small>
                    </div>
                </div>
                
                <!-- 总数量 -->
                <div class="attribute-group">
                    <h4>📦 总数量</h4>
                    <div class="custom-input-group">
                        <label for="quantity">请输入总数量：</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary" onclick="changeQuantity(-1)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" 
                                   id="quantity" 
                                   class="custom-input" 
                                   value="1" 
                                   min="1" 
                                   max="1000"
                                   style="text-align: center;">
                            <button type="button" class="btn btn-outline-secondary" onclick="changeQuantity(1)">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <small class="text-muted">单位：本</small>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：价格显示 -->
            <div>
                <div class="price-display">
                    <div class="price-label">总价格</div>
                    <div class="price-value" id="totalPrice">¥10.00</div>
                    
                    <div class="price-breakdown" id="priceBreakdown">
                        <div><strong>价格明细：</strong></div>
                        <div>基础价格：¥10.00</div>
                        <div>尺寸调整：¥0.00</div>
                        <div>纸张调整：¥0.00</div>
                        <div>张数费用：¥24.00 (100张 × ¥0.24)</div>
                        <div>数量：1本</div>
                        <hr>
                        <div><strong>总计：¥34.00</strong></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="demo-actions">
        <button class="demo-btn" onclick="resetDemo()">
            <i class="fas fa-redo me-2"></i>重置演示
        </button>
        <button class="demo-btn secondary" onclick="window.location.href='/'">
            <i class="fas fa-home me-2"></i>返回首页
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 演示数据
let selectedAttributes = {
    size: null,
    paper: null,
    pages: 100
};
let basePrice = 10.00;
let quantity = 1;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDemo();
});

function initializeDemo() {
    // 绑定固定选项点击事件
    document.querySelectorAll('.option-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            selectOption(this);
        });
    });
    
    // 绑定自定义输入事件
    document.getElementById('pages').addEventListener('input', function() {
        validateAndUpdatePages(this);
    });
    
    document.getElementById('quantity').addEventListener('input', function() {
        updateQuantity(this.value);
    });
    
    // 初始计算价格
    calculatePrice();
}

function selectOption(btn) {
    const group = btn.dataset.group;
    const value = btn.dataset.value;
    const price = parseFloat(btn.dataset.price) || 0;
    
    // 清除同组其他选择
    document.querySelectorAll(`[data-group="${group}"]`).forEach(b => {
        b.classList.remove('selected');
    });
    
    // 选中当前选项
    btn.classList.add('selected');
    selectedAttributes[group] = { value, price };
    
    calculatePrice();
}

function validateAndUpdatePages(input) {
    const value = parseInt(input.value);
    const feedback = document.getElementById('pages-feedback');
    
    if (isNaN(value) || value < 1) {
        input.classList.remove('valid');
        input.classList.add('invalid');
        feedback.textContent = '请输入有效的张数（大于0的整数）';
        feedback.className = 'input-feedback invalid';
        selectedAttributes.pages = null;
    } else if (value > 9999) {
        input.classList.remove('valid');
        input.classList.add('invalid');
        feedback.textContent = '张数不能超过9999';
        feedback.className = 'input-feedback invalid';
        selectedAttributes.pages = null;
    } else {
        input.classList.remove('invalid');
        input.classList.add('valid');
        feedback.textContent = '✓ 输入有效';
        feedback.className = 'input-feedback valid';
        selectedAttributes.pages = value;
    }
    
    calculatePrice();
}

function changeQuantity(delta) {
    const input = document.getElementById('quantity');
    const newValue = Math.max(1, Math.min(1000, quantity + delta));
    input.value = newValue;
    updateQuantity(newValue);
}

function updateQuantity(value) {
    const newQuantity = parseInt(value);
    if (newQuantity >= 1 && newQuantity <= 1000) {
        quantity = newQuantity;
        calculatePrice();
    }
}

function calculatePrice() {
    let total = basePrice;
    let breakdown = [`基础价格：¥${basePrice.toFixed(2)}`];
    
    // 尺寸调整
    if (selectedAttributes.size) {
        const sizeAdjust = selectedAttributes.size.price;
        total += sizeAdjust;
        breakdown.push(`尺寸调整：¥${sizeAdjust.toFixed(2)} (${selectedAttributes.size.value})`);
    } else {
        breakdown.push('尺寸调整：¥0.00 (未选择)');
    }
    
    // 纸张调整
    if (selectedAttributes.paper) {
        const paperAdjust = selectedAttributes.paper.price;
        total += paperAdjust;
        breakdown.push(`纸张调整：¥${paperAdjust.toFixed(2)} (${selectedAttributes.paper.value})`);
    } else {
        breakdown.push('纸张调整：¥0.00 (未选择)');
    }
    
    // 张数费用
    if (selectedAttributes.pages) {
        const pagesCost = selectedAttributes.pages * 0.24;
        total += pagesCost;
        breakdown.push(`张数费用：¥${pagesCost.toFixed(2)} (${selectedAttributes.pages}张 × ¥0.24)`);
    } else {
        breakdown.push('张数费用：¥0.00 (未输入)');
    }
    
    // 数量
    const finalTotal = total * quantity;
    breakdown.push(`数量：${quantity}本`);
    
    // 更新显示
    document.getElementById('totalPrice').textContent = `¥${finalTotal.toFixed(2)}`;
    
    const breakdownHtml = [
        '<div><strong>价格明细：</strong></div>',
        ...breakdown.map(item => `<div>${item}</div>`),
        '<hr>',
        `<div><strong>总计：¥${finalTotal.toFixed(2)}</strong></div>`
    ].join('');
    
    document.getElementById('priceBreakdown').innerHTML = breakdownHtml;
}

function resetDemo() {
    // 清除所有选择
    document.querySelectorAll('.option-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    // 重置输入
    document.getElementById('pages').value = '100';
    document.getElementById('pages').classList.remove('valid', 'invalid');
    document.getElementById('pages-feedback').textContent = '';
    
    document.getElementById('quantity').value = '1';
    
    // 重置数据
    selectedAttributes = { size: null, paper: null, pages: 100 };
    quantity = 1;
    
    // 重新计算价格
    calculatePrice();
}
</script>
{% endblock %}
