/**
 * 简化版省市县三级联动选择器
 * 重新设计，更加可靠和简单
 */
class SimpleRegionSelector {
    constructor(options) {
        this.options = {
            provinceSelector: '#province',
            citySelector: '#city', 
            districtSelector: '#district',
            provincePlaceholder: '请选择省份',
            cityPlaceholder: '请选择城市',
            districtPlaceholder: '请选择区县',
            onChange: null,
            ...options
        };
        
        this.provinceSelect = document.querySelector(this.options.provinceSelector);
        this.citySelect = document.querySelector(this.options.citySelector);
        this.districtSelect = document.querySelector(this.options.districtSelector);
        
        if (!this.provinceSelect || !this.citySelect || !this.districtSelect) {
            console.error('找不到选择器元素');
            return;
        }
        
        this.init();
    }
    
    init() {
        console.log('初始化简化版区域选择器...');
        
        // 绑定事件
        this.provinceSelect.addEventListener('change', () => this.onProvinceChange());
        this.citySelect.addEventListener('change', () => this.onCityChange());
        this.districtSelect.addEventListener('change', () => this.onDistrictChange());
        
        // 初始化选择框
        this.resetSelect(this.provinceSelect, this.options.provincePlaceholder);
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        this.resetSelect(this.districtSelect, this.options.districtPlaceholder);
        
        // 禁用城市和区县选择框
        this.citySelect.disabled = true;
        this.districtSelect.disabled = true;
        
        // 加载省份数据
        this.loadProvinces();
    }
    
    resetSelect(select, placeholder) {
        select.innerHTML = `<option value="">${placeholder}</option>`;
    }
    
    async loadProvinces() {
        console.log('开始加载省份数据...');
        try {
            const response = await fetch('/api/regions/provinces');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const provinces = await response.json();
            console.log('省份数据加载成功:', provinces.length, '个省份');
            
            this.renderProvinces(provinces);
        } catch (error) {
            console.error('加载省份数据失败:', error);
            this.showError('加载省份数据失败');
        }
    }
    
    renderProvinces(provinces) {
        this.resetSelect(this.provinceSelect, this.options.provincePlaceholder);
        
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.name;
            this.provinceSelect.appendChild(option);
        });
        
        console.log('省份选项渲染完成');
    }
    
    async onProvinceChange() {
        const provinceId = this.provinceSelect.value;
        console.log('省份选择变化:', provinceId);
        
        // 重置城市和区县
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        this.resetSelect(this.districtSelect, this.options.districtPlaceholder);
        this.citySelect.disabled = true;
        this.districtSelect.disabled = true;
        
        if (!provinceId) {
            this.triggerChange();
            return;
        }
        
        await this.loadCities(provinceId);
    }
    
    async loadCities(provinceId) {
        console.log('开始加载城市数据，省份ID:', provinceId);
        try {
            const response = await fetch(`/api/regions/cities/${provinceId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const cities = await response.json();
            console.log('城市数据加载成功:', cities.length, '个城市');
            
            this.renderCities(cities);
        } catch (error) {
            console.error('加载城市数据失败:', error);
            this.showError('加载城市数据失败');
        }
    }
    
    renderCities(cities) {
        this.resetSelect(this.citySelect, this.options.cityPlaceholder);
        
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            this.citySelect.appendChild(option);
        });
        
        this.citySelect.disabled = cities.length === 0;
        console.log('城市选项渲染完成');
        this.triggerChange();
    }
    
    async onCityChange() {
        const cityId = this.citySelect.value;
        console.log('城市选择变化:', cityId);
        
        // 重置区县
        this.resetSelect(this.districtSelect, this.options.districtPlaceholder);
        this.districtSelect.disabled = true;
        
        if (!cityId) {
            this.triggerChange();
            return;
        }
        
        await this.loadDistricts(cityId);
    }
    
    async loadDistricts(cityId) {
        console.log('开始加载区县数据，城市ID:', cityId);
        try {
            const response = await fetch(`/api/regions/districts/${cityId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const districts = await response.json();
            console.log('区县数据加载成功:', districts.length, '个区县');
            
            this.renderDistricts(districts);
        } catch (error) {
            console.error('加载区县数据失败:', error);
            this.showError('加载区县数据失败');
        }
    }
    
    renderDistricts(districts) {
        this.resetSelect(this.districtSelect, this.options.districtPlaceholder);
        
        districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.id;
            option.textContent = district.name;
            this.districtSelect.appendChild(option);
        });
        
        this.districtSelect.disabled = districts.length === 0;
        console.log('区县选项渲染完成');
        this.triggerChange();
    }
    
    onDistrictChange() {
        console.log('区县选择变化:', this.districtSelect.value);
        this.triggerChange();
    }
    
    triggerChange() {
        if (typeof this.options.onChange === 'function') {
            const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
            const city = this.citySelect.options[this.citySelect.selectedIndex];
            const district = this.districtSelect.options[this.districtSelect.selectedIndex];
            
            const result = {
                province: {
                    id: this.provinceSelect.value,
                    name: province && province.value ? province.textContent : ''
                },
                city: {
                    id: this.citySelect.value,
                    name: city && city.value ? city.textContent : ''
                },
                district: {
                    id: this.districtSelect.value,
                    name: district && district.value ? district.textContent : ''
                }
            };
            
            console.log('触发变化事件:', result);
            this.options.onChange(result);
        }
    }
    
    showError(message) {
        console.error(message);
        alert(message); // 简单的错误提示
    }
    
    getSelectedRegion() {
        const province = this.provinceSelect.options[this.provinceSelect.selectedIndex];
        const city = this.citySelect.options[this.citySelect.selectedIndex];
        const district = this.districtSelect.options[this.districtSelect.selectedIndex];
        
        return {
            province: {
                id: this.provinceSelect.value,
                name: province && province.value ? province.textContent : ''
            },
            city: {
                id: this.citySelect.value,
                name: city && city.value ? city.textContent : ''
            },
            district: {
                id: this.districtSelect.value,
                name: district && district.value ? district.textContent : ''
            }
        };
    }
}

// 挂载到全局
if (typeof window !== 'undefined') {
    window.SimpleRegionSelector = SimpleRegionSelector;
}
