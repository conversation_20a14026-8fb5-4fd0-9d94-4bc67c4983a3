2025-06-02 13:10:58,478 - admin_operations - ERROR - 属性绑定错误: {'operation': 'attribute_binding', 'type': 'summary', 'context': {'timestamp': '2025-06-02T13:10:58.478283', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/products/add?category_id=3', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'binding_data': {'product_id': 11, 'attribute_id': 'batch_operation'}, 'details': {'new_bindings': 3, 'duplicates_skipped': 0, 'total_selected': 3, 'skipped_attributes': [], 'summary': '新增3个属性绑定'}}
2025-06-02 13:10:58,485 - admin_operations - INFO - 商品添加成功: {'operation': 'add_product', 'success': True, 'context': {'timestamp': '2025-06-02T13:10:58.484266', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/products/add?category_id=3', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'form_data': {'name': '111', 'category_id': 3, 'base_price': Decimal('1'), 'selected_attributes_count': 6, 'has_image_file': True, 'image_url': None}}
2025-06-02 13:46:07,205 - admin_operations - WARNING - 表单验证失败: {'operation': 'add_product', 'type': 'validation_error', 'context': {'timestamp': '2025-06-02T13:46:07.205388', 'method': 'POST', 'url': 'http://hudjn.w1.luyouxia.net/admin/products/add?category_id=4', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'form_errors': {'base_price': ['价格必须大于0']}}
2025-06-02 13:46:19,963 - admin_operations - ERROR - 属性绑定错误: {'operation': 'attribute_binding', 'type': 'summary', 'context': {'timestamp': '2025-06-02T13:46:19.963866', 'method': 'POST', 'url': 'http://hudjn.w1.luyouxia.net/admin/products/add?category_id=4', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'binding_data': {'product_id': 12, 'attribute_id': 'batch_operation'}, 'details': {'new_bindings': 12, 'duplicates_skipped': 0, 'total_selected': 12, 'skipped_attributes': [], 'summary': '新增12个属性绑定'}}
2025-06-02 13:46:19,985 - admin_operations - INFO - 商品添加成功: {'operation': 'add_product', 'success': True, 'context': {'timestamp': '2025-06-02T13:46:19.984810', 'method': 'POST', 'url': 'http://hudjn.w1.luyouxia.net/admin/products/add?category_id=4', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'form_data': {'name': '爆款联单', 'category_id': 4, 'base_price': Decimal('1'), 'selected_attributes_count': 24, 'has_image_file': False, 'image_url': None}}
2025-06-02 16:09:25,817 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:09:25.815480', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('2.25000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 1, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'ImY2NzhkZDE1OTUwOThmNDQ2ZTQ5Zjc0OWU5YTBlZDFiYmIyMjNiNTMi.aD1b_Q.IFmysiPZIoeF5sUm0uV9_rJk4ik'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 2.25, 'price_modifier_type': 'fixed', 'price_formula': None, 'is_quantity_based': False, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:10:02,163 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:10:02.162689', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('2.25000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 2.25', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 1, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'ImY2NzhkZDE1OTUwOThmNDQ2ZTQ5Zjc0OWU5YTBlZDFiYmIyMjNiNTMi.aD1cNQ.RExovy-jyvbuDDAiQM0v8Jqz2B4'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:10:33,131 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:10:33.130959', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('2.25000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 1, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'ImY2NzhkZDE1OTUwOThmNDQ2ZTQ5Zjc0OWU5YTBlZDFiYmIyMjNiNTMi.aD1cag.gfKJx2zyHPee7I911hG0rBMK4ow'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 2.25', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:21:13,893 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:21:13.891011', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1eHQ.Mbvva5AiGz-UXc7d-ihCvJ9NlVI'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:21:16,567 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:21:16.566256', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1e-Q.tkrKOf3TNQgWB44rFJMGok70A4I'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:21:25,984 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:21:25.983961', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1e_A.YEB8lFJC_8OQhLXMuy-evvCgHK4'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:22:48,324 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:22:48.322945', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1fBQ.MSLJG4pB9g1fZi-W01OBS7leDco'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:24:05,236 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:24:05.235719', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1fWA.XOL8j1j0uLPgZwXz3aGP1uUeXHY'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:24:22,352 - admin_operations - ERROR - 数据库操作错误: {'operation': 'edit_attribute', 'type': 'database_error', 'context': {'timestamp': '2025-06-02T16:24:22.351176', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'error_details': {'error_type': 'AttributeError', 'error_message': "'AdminLogger' object has no attribute 'log_admin_action'", 'error_class': 'builtins.AttributeError'}, 'additional_data': {'attribute_id': 22, 'form_data': {'group_id': 19, 'name': '50', 'value': '50', 'calculation_type': 'formula', 'price_modifier': Decimal('0.00000'), 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'min_quantity': 50, 'max_quantity': 9999, 'sort_order': 0, 'is_active': True, 'submit': False, 'csrf_token': 'IjNmMDhhMTM5ZDBiN2MxMzc5MGNhMzQ4YWIwOWQxNzI2OWQ1ZTg0MDIi.aD1fsg.mVfCnIj5xWsiw3sWMRn66LZJmnw'}, 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:39:17,555 - admin_operations - INFO - 管理操作: {'operation': 'edit_attribute', 'details': '编辑属性: 测试属性 4 (值: 测试值 4)', 'context': {'timestamp': '2025-06-02T16:39:17.554095', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/1/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'python-requests/2.31.0', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 1, 'calculation_type': 'formula', 'old_data': {'name': '测试属性 4', 'value': '测试值 4', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 2.5', 'is_quantity_based': True, 'quantity_unit': '套', 'is_active': True}, 'new_data': {'name': '测试属性 4', 'value': '测试值 4', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 3.5', 'is_quantity_based': True, 'quantity_unit': '套', 'is_active': True}}}
2025-06-02 16:39:35,589 - admin_operations - INFO - 管理操作: {'operation': 'edit_attribute', 'details': '编辑属性: 50 (值: 50)', 'context': {'timestamp': '2025-06-02T16:39:35.587806', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 22, 'calculation_type': 'formula', 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}, 'new_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 16:39:44,842 - admin_operations - INFO - 管理操作: {'operation': 'edit_attribute', 'details': '编辑属性: 50 (值: 50)', 'context': {'timestamp': '2025-06-02T16:39:44.841768', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/22/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 22, 'calculation_type': 'formula', 'old_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}, 'new_data': {'name': '50', 'value': '50', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity*0.24', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-02 17:06:03,393 - admin_operations - INFO - 管理操作: {'operation': 'add_attribute', 'details': '添加属性: 套数 (值: 套数)', 'context': {'timestamp': '2025-06-02T17:06:03.390981', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/add?group_id=19&from_group=19&locked=True', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 33, 'calculation_type': 'formula', 'is_quantity_based': True, 'price_formula': 'quantity * 0.25'}}
2025-06-03 11:39:07,709 - admin_operations - INFO - 管理操作: {'operation': 'edit_attribute', 'details': '编辑属性: 侧边联一样 (值: 侧边联不一样)', 'context': {'timestamp': '2025-06-03T11:39:07.707682', 'method': 'POST', 'url': 'http://hudjn.w1.luyouxia.net/admin/attributes/17/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 17, 'calculation_type': 'fixed', 'old_data': {'name': '侧边联一样', 'value': '侧边联一样', 'price_modifier': 30.0, 'price_modifier_type': 'fixed', 'price_formula': None, 'is_quantity_based': False, 'quantity_unit': '个', 'is_active': True}, 'new_data': {'name': '侧边联一样', 'value': '侧边联不一样', 'price_modifier': 30.0, 'price_modifier_type': 'fixed', 'price_formula': None, 'is_quantity_based': False, 'quantity_unit': '个', 'is_active': True}}}
2025-06-03 19:19:50,010 - admin_operations - INFO - 管理操作: {'operation': 'add_attribute', 'details': '添加属性: 测试支付 (值: 测试支付)', 'context': {'timestamp': '2025-06-03T19:19:50.009455', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/add?group_id=24&from_group=24&locked=True', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 34, 'calculation_type': 'fixed', 'is_quantity_based': False, 'price_formula': None}}
2025-06-04 11:49:49,283 - admin_operations - INFO - 管理操作: {'operation': 'edit_attribute', 'details': '编辑属性: 每本张数 (值: 套数)', 'context': {'timestamp': '2025-06-04T11:49:49.282463', 'method': 'POST', 'url': 'http://hudjn.w1.luyouxia.net/admin/attributes/33/edit', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 33, 'calculation_type': 'formula', 'old_data': {'name': '套数', 'value': '套数', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 0.25', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}, 'new_data': {'name': '每本张数', 'value': '套数', 'price_modifier': 0.0, 'price_modifier_type': 'fixed', 'price_formula': 'quantity * 0.25', 'is_quantity_based': True, 'quantity_unit': '个', 'is_active': True}}}
2025-06-15 18:27:08,040 - admin_operations - INFO - 管理操作: {'operation': 'add_attribute', 'details': '添加属性: 颜色 (值: 颜色)', 'context': {'timestamp': '2025-06-15T18:27:08.039786', 'method': 'POST', 'url': 'http://127.0.0.1:5000/admin/attributes/add?group_id=28&from_group=28&locked=True', 'remote_addr': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0', 'user_id': 1, 'username': 'admin', 'is_admin': True}, 'additional_data': {'attribute_id': 43, 'calculation_type': 'fixed', 'is_quantity_based': False, 'price_formula': None}}
