#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入全国完整的省市县数据
包含所有省份、城市和区县信息
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

def import_full_regions():
    """导入全国完整区域数据"""
    app = create_app()
    with app.app_context():
        # 清空现有数据
        print("正在清空现有区域数据...")
        Region.query.delete()
        db.session.commit()
        print("现有数据已清空")
        
        # 全国省份数据
        provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '130000', 'name': '河北省'},
            {'id': '140000', 'name': '山西省'},
            {'id': '150000', 'name': '内蒙古自治区'},
            {'id': '210000', 'name': '辽宁省'},
            {'id': '220000', 'name': '吉林省'},
            {'id': '230000', 'name': '黑龙江省'},
            {'id': '310000', 'name': '上海市'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
            {'id': '340000', 'name': '安徽省'},
            {'id': '350000', 'name': '福建省'},
            {'id': '360000', 'name': '江西省'},
            {'id': '370000', 'name': '山东省'},
            {'id': '410000', 'name': '河南省'},
            {'id': '420000', 'name': '湖北省'},
            {'id': '430000', 'name': '湖南省'},
            {'id': '440000', 'name': '广东省'},
            {'id': '450000', 'name': '广西壮族自治区'},
            {'id': '460000', 'name': '海南省'},
            {'id': '500000', 'name': '重庆市'},
            {'id': '510000', 'name': '四川省'},
            {'id': '520000', 'name': '贵州省'},
            {'id': '530000', 'name': '云南省'},
            {'id': '540000', 'name': '西藏自治区'},
            {'id': '610000', 'name': '陕西省'},
            {'id': '620000', 'name': '甘肃省'},
            {'id': '630000', 'name': '青海省'},
            {'id': '640000', 'name': '宁夏回族自治区'},
            {'id': '650000', 'name': '新疆维吾尔自治区'},
            {'id': '710000', 'name': '台湾省'},
            {'id': '810000', 'name': '香港特别行政区'},
            {'id': '820000', 'name': '澳门特别行政区'}
        ]
        
        # 全国主要城市数据
        cities = [
            # 北京市
            {'id': '110100', 'name': '北京市', 'parent_id': '110000'},
            
            # 天津市
            {'id': '120100', 'name': '天津市', 'parent_id': '120000'},
            
            # 河北省
            {'id': '130100', 'name': '石家庄市', 'parent_id': '130000'},
            {'id': '130200', 'name': '唐山市', 'parent_id': '130000'},
            {'id': '130300', 'name': '秦皇岛市', 'parent_id': '130000'},
            {'id': '130400', 'name': '邯郸市', 'parent_id': '130000'},
            {'id': '130500', 'name': '邢台市', 'parent_id': '130000'},
            {'id': '130600', 'name': '保定市', 'parent_id': '130000'},
            {'id': '130700', 'name': '张家口市', 'parent_id': '130000'},
            {'id': '130800', 'name': '承德市', 'parent_id': '130000'},
            {'id': '130900', 'name': '沧州市', 'parent_id': '130000'},
            {'id': '131000', 'name': '廊坊市', 'parent_id': '130000'},
            {'id': '131100', 'name': '衡水市', 'parent_id': '130000'},
            
            # 山西省
            {'id': '140100', 'name': '太原市', 'parent_id': '140000'},
            {'id': '140200', 'name': '大同市', 'parent_id': '140000'},
            {'id': '140300', 'name': '阳泉市', 'parent_id': '140000'},
            {'id': '140400', 'name': '长治市', 'parent_id': '140000'},
            {'id': '140500', 'name': '晋城市', 'parent_id': '140000'},
            {'id': '140600', 'name': '朔州市', 'parent_id': '140000'},
            {'id': '140700', 'name': '晋中市', 'parent_id': '140000'},
            {'id': '140800', 'name': '运城市', 'parent_id': '140000'},
            {'id': '140900', 'name': '忻州市', 'parent_id': '140000'},
            {'id': '141000', 'name': '临汾市', 'parent_id': '140000'},
            {'id': '141100', 'name': '吕梁市', 'parent_id': '140000'},
            
            # 内蒙古自治区
            {'id': '150100', 'name': '呼和浩特市', 'parent_id': '150000'},
            {'id': '150200', 'name': '包头市', 'parent_id': '150000'},
            {'id': '150300', 'name': '乌海市', 'parent_id': '150000'},
            {'id': '150400', 'name': '赤峰市', 'parent_id': '150000'},
            {'id': '150500', 'name': '通辽市', 'parent_id': '150000'},
            {'id': '150600', 'name': '鄂尔多斯市', 'parent_id': '150000'},
            {'id': '150700', 'name': '呼伦贝尔市', 'parent_id': '150000'},
            {'id': '150800', 'name': '巴彦淖尔市', 'parent_id': '150000'},
            {'id': '150900', 'name': '乌兰察布市', 'parent_id': '150000'},
            {'id': '152200', 'name': '兴安盟', 'parent_id': '150000'},
            {'id': '152500', 'name': '锡林郭勒盟', 'parent_id': '150000'},
            {'id': '152900', 'name': '阿拉善盟', 'parent_id': '150000'},
            
            # 辽宁省
            {'id': '210100', 'name': '沈阳市', 'parent_id': '210000'},
            {'id': '210200', 'name': '大连市', 'parent_id': '210000'},
            {'id': '210300', 'name': '鞍山市', 'parent_id': '210000'},
            {'id': '210400', 'name': '抚顺市', 'parent_id': '210000'},
            {'id': '210500', 'name': '本溪市', 'parent_id': '210000'},
            {'id': '210600', 'name': '丹东市', 'parent_id': '210000'},
            {'id': '210700', 'name': '锦州市', 'parent_id': '210000'},
            {'id': '210800', 'name': '营口市', 'parent_id': '210000'},
            {'id': '210900', 'name': '阜新市', 'parent_id': '210000'},
            {'id': '211000', 'name': '辽阳市', 'parent_id': '210000'},
            {'id': '211100', 'name': '盘锦市', 'parent_id': '210000'},
            {'id': '211200', 'name': '铁岭市', 'parent_id': '210000'},
            {'id': '211300', 'name': '朝阳市', 'parent_id': '210000'},
            {'id': '211400', 'name': '葫芦岛市', 'parent_id': '210000'},
            
            # 吉林省
            {'id': '220100', 'name': '长春市', 'parent_id': '220000'},
            {'id': '220200', 'name': '吉林市', 'parent_id': '220000'},
            {'id': '220300', 'name': '四平市', 'parent_id': '220000'},
            {'id': '220400', 'name': '辽源市', 'parent_id': '220000'},
            {'id': '220500', 'name': '通化市', 'parent_id': '220000'},
            {'id': '220600', 'name': '白山市', 'parent_id': '220000'},
            {'id': '220700', 'name': '松原市', 'parent_id': '220000'},
            {'id': '220800', 'name': '白城市', 'parent_id': '220000'},
            {'id': '222400', 'name': '延边朝鲜族自治州', 'parent_id': '220000'},
            
            # 黑龙江省
            {'id': '230100', 'name': '哈尔滨市', 'parent_id': '230000'},
            {'id': '230200', 'name': '齐齐哈尔市', 'parent_id': '230000'},
            {'id': '230300', 'name': '鸡西市', 'parent_id': '230000'},
            {'id': '230400', 'name': '鹤岗市', 'parent_id': '230000'},
            {'id': '230500', 'name': '双鸭山市', 'parent_id': '230000'},
            {'id': '230600', 'name': '大庆市', 'parent_id': '230000'},
            {'id': '230700', 'name': '伊春市', 'parent_id': '230000'},
            {'id': '230800', 'name': '佳木斯市', 'parent_id': '230000'},
            {'id': '230900', 'name': '七台河市', 'parent_id': '230000'},
            {'id': '231000', 'name': '牡丹江市', 'parent_id': '230000'},
            {'id': '231100', 'name': '黑河市', 'parent_id': '230000'},
            {'id': '231200', 'name': '绥化市', 'parent_id': '230000'},
            {'id': '232700', 'name': '大兴安岭地区', 'parent_id': '230000'},
            
            # 上海市
            {'id': '310100', 'name': '上海市', 'parent_id': '310000'},

            # 江苏省
            {'id': '320100', 'name': '南京市', 'parent_id': '320000'},
            {'id': '320200', 'name': '无锡市', 'parent_id': '320000'},
            {'id': '320300', 'name': '徐州市', 'parent_id': '320000'},
            {'id': '320400', 'name': '常州市', 'parent_id': '320000'},
            {'id': '320500', 'name': '苏州市', 'parent_id': '320000'},
            {'id': '320600', 'name': '南通市', 'parent_id': '320000'},
            {'id': '320700', 'name': '连云港市', 'parent_id': '320000'},
            {'id': '320800', 'name': '淮安市', 'parent_id': '320000'},
            {'id': '320900', 'name': '盐城市', 'parent_id': '320000'},
            {'id': '321000', 'name': '扬州市', 'parent_id': '320000'},
            {'id': '321100', 'name': '镇江市', 'parent_id': '320000'},
            {'id': '321200', 'name': '泰州市', 'parent_id': '320000'},
            {'id': '321300', 'name': '宿迁市', 'parent_id': '320000'},

            # 浙江省
            {'id': '330100', 'name': '杭州市', 'parent_id': '330000'},
            {'id': '330200', 'name': '宁波市', 'parent_id': '330000'},
            {'id': '330300', 'name': '温州市', 'parent_id': '330000'},
            {'id': '330400', 'name': '嘉兴市', 'parent_id': '330000'},
            {'id': '330500', 'name': '湖州市', 'parent_id': '330000'},
            {'id': '330600', 'name': '绍兴市', 'parent_id': '330000'},
            {'id': '330700', 'name': '金华市', 'parent_id': '330000'},
            {'id': '330800', 'name': '衢州市', 'parent_id': '330000'},
            {'id': '330900', 'name': '舟山市', 'parent_id': '330000'},
            {'id': '331000', 'name': '台州市', 'parent_id': '330000'},
            {'id': '331100', 'name': '丽水市', 'parent_id': '330000'},

            # 安徽省
            {'id': '340100', 'name': '合肥市', 'parent_id': '340000'},
            {'id': '340200', 'name': '芜湖市', 'parent_id': '340000'},
            {'id': '340300', 'name': '蚌埠市', 'parent_id': '340000'},
            {'id': '340400', 'name': '淮南市', 'parent_id': '340000'},
            {'id': '340500', 'name': '马鞍山市', 'parent_id': '340000'},
            {'id': '340600', 'name': '淮北市', 'parent_id': '340000'},
            {'id': '340700', 'name': '铜陵市', 'parent_id': '340000'},
            {'id': '340800', 'name': '安庆市', 'parent_id': '340000'},
            {'id': '341000', 'name': '黄山市', 'parent_id': '340000'},
            {'id': '341100', 'name': '滁州市', 'parent_id': '340000'},
            {'id': '341200', 'name': '阜阳市', 'parent_id': '340000'},
            {'id': '341300', 'name': '宿州市', 'parent_id': '340000'},
            {'id': '341500', 'name': '六安市', 'parent_id': '340000'},
            {'id': '341600', 'name': '亳州市', 'parent_id': '340000'},
            {'id': '341700', 'name': '池州市', 'parent_id': '340000'},
            {'id': '341800', 'name': '宣城市', 'parent_id': '340000'},

            # 福建省
            {'id': '350100', 'name': '福州市', 'parent_id': '350000'},
            {'id': '350200', 'name': '厦门市', 'parent_id': '350000'},
            {'id': '350300', 'name': '莆田市', 'parent_id': '350000'},
            {'id': '350400', 'name': '三明市', 'parent_id': '350000'},
            {'id': '350500', 'name': '泉州市', 'parent_id': '350000'},
            {'id': '350600', 'name': '漳州市', 'parent_id': '350000'},
            {'id': '350700', 'name': '南平市', 'parent_id': '350000'},
            {'id': '350800', 'name': '龙岩市', 'parent_id': '350000'},
            {'id': '350900', 'name': '宁德市', 'parent_id': '350000'},

            # 江西省
            {'id': '360100', 'name': '南昌市', 'parent_id': '360000'},
            {'id': '360200', 'name': '景德镇市', 'parent_id': '360000'},
            {'id': '360300', 'name': '萍乡市', 'parent_id': '360000'},
            {'id': '360400', 'name': '九江市', 'parent_id': '360000'},
            {'id': '360500', 'name': '新余市', 'parent_id': '360000'},
            {'id': '360600', 'name': '鹰潭市', 'parent_id': '360000'},
            {'id': '360700', 'name': '赣州市', 'parent_id': '360000'},
            {'id': '360800', 'name': '吉安市', 'parent_id': '360000'},
            {'id': '360900', 'name': '宜春市', 'parent_id': '360000'},
            {'id': '361000', 'name': '抚州市', 'parent_id': '360000'},
            {'id': '361100', 'name': '上饶市', 'parent_id': '360000'},

            # 山东省
            {'id': '370100', 'name': '济南市', 'parent_id': '370000'},
            {'id': '370200', 'name': '青岛市', 'parent_id': '370000'},
            {'id': '370300', 'name': '淄博市', 'parent_id': '370000'},
            {'id': '370400', 'name': '枣庄市', 'parent_id': '370000'},
            {'id': '370500', 'name': '东营市', 'parent_id': '370000'},
            {'id': '370600', 'name': '烟台市', 'parent_id': '370000'},
            {'id': '370700', 'name': '潍坊市', 'parent_id': '370000'},
            {'id': '370800', 'name': '济宁市', 'parent_id': '370000'},
            {'id': '370900', 'name': '泰安市', 'parent_id': '370000'},
            {'id': '371000', 'name': '威海市', 'parent_id': '370000'},
            {'id': '371100', 'name': '日照市', 'parent_id': '370000'},
            {'id': '371300', 'name': '临沂市', 'parent_id': '370000'},
            {'id': '371400', 'name': '德州市', 'parent_id': '370000'},
            {'id': '371500', 'name': '聊城市', 'parent_id': '370000'},
            {'id': '371600', 'name': '滨州市', 'parent_id': '370000'},
            {'id': '371700', 'name': '菏泽市', 'parent_id': '370000'},

            # 河南省
            {'id': '410100', 'name': '郑州市', 'parent_id': '410000'},
            {'id': '410200', 'name': '开封市', 'parent_id': '410000'},
            {'id': '410300', 'name': '洛阳市', 'parent_id': '410000'},
            {'id': '410400', 'name': '平顶山市', 'parent_id': '410000'},
            {'id': '410500', 'name': '安阳市', 'parent_id': '410000'},
            {'id': '410600', 'name': '鹤壁市', 'parent_id': '410000'},
            {'id': '410700', 'name': '新乡市', 'parent_id': '410000'},
            {'id': '410800', 'name': '焦作市', 'parent_id': '410000'},
            {'id': '410900', 'name': '濮阳市', 'parent_id': '410000'},
            {'id': '411000', 'name': '许昌市', 'parent_id': '410000'},
            {'id': '411100', 'name': '漯河市', 'parent_id': '410000'},
            {'id': '411200', 'name': '三门峡市', 'parent_id': '410000'},
            {'id': '411300', 'name': '南阳市', 'parent_id': '410000'},
            {'id': '411400', 'name': '商丘市', 'parent_id': '410000'},
            {'id': '411500', 'name': '信阳市', 'parent_id': '410000'},
            {'id': '411600', 'name': '周口市', 'parent_id': '410000'},
            {'id': '411700', 'name': '驻马店市', 'parent_id': '410000'},
            {'id': '419001', 'name': '济源市', 'parent_id': '410000'},

            # 湖北省
            {'id': '420100', 'name': '武汉市', 'parent_id': '420000'},
            {'id': '420200', 'name': '黄石市', 'parent_id': '420000'},
            {'id': '420300', 'name': '十堰市', 'parent_id': '420000'},
            {'id': '420500', 'name': '宜昌市', 'parent_id': '420000'},
            {'id': '420600', 'name': '襄阳市', 'parent_id': '420000'},
            {'id': '420700', 'name': '鄂州市', 'parent_id': '420000'},
            {'id': '420800', 'name': '荆门市', 'parent_id': '420000'},
            {'id': '420900', 'name': '孝感市', 'parent_id': '420000'},
            {'id': '421000', 'name': '荆州市', 'parent_id': '420000'},
            {'id': '421100', 'name': '黄冈市', 'parent_id': '420000'},
            {'id': '421200', 'name': '咸宁市', 'parent_id': '420000'},
            {'id': '421300', 'name': '随州市', 'parent_id': '420000'},
            {'id': '422800', 'name': '恩施土家族苗族自治州', 'parent_id': '420000'},
            {'id': '429004', 'name': '仙桃市', 'parent_id': '420000'},
            {'id': '429005', 'name': '潜江市', 'parent_id': '420000'},
            {'id': '429006', 'name': '天门市', 'parent_id': '420000'},
            {'id': '429021', 'name': '神农架林区', 'parent_id': '420000'},

            # 湖南省
            {'id': '430100', 'name': '长沙市', 'parent_id': '430000'},
            {'id': '430200', 'name': '株洲市', 'parent_id': '430000'},
            {'id': '430300', 'name': '湘潭市', 'parent_id': '430000'},
            {'id': '430400', 'name': '衡阳市', 'parent_id': '430000'},
            {'id': '430500', 'name': '邵阳市', 'parent_id': '430000'},
            {'id': '430600', 'name': '岳阳市', 'parent_id': '430000'},
            {'id': '430700', 'name': '常德市', 'parent_id': '430000'},
            {'id': '430800', 'name': '张家界市', 'parent_id': '430000'},
            {'id': '430900', 'name': '益阳市', 'parent_id': '430000'},
            {'id': '431000', 'name': '郴州市', 'parent_id': '430000'},
            {'id': '431100', 'name': '永州市', 'parent_id': '430000'},
            {'id': '431200', 'name': '怀化市', 'parent_id': '430000'},
            {'id': '431300', 'name': '娄底市', 'parent_id': '430000'},
            {'id': '433100', 'name': '湘西土家族苗族自治州', 'parent_id': '430000'},

            # 广东省
            {'id': '440100', 'name': '广州市', 'parent_id': '440000'},
            {'id': '440200', 'name': '韶关市', 'parent_id': '440000'},
            {'id': '440300', 'name': '深圳市', 'parent_id': '440000'},
            {'id': '440400', 'name': '珠海市', 'parent_id': '440000'},
            {'id': '440500', 'name': '汕头市', 'parent_id': '440000'},
            {'id': '440600', 'name': '佛山市', 'parent_id': '440000'},
            {'id': '440700', 'name': '江门市', 'parent_id': '440000'},
            {'id': '440800', 'name': '湛江市', 'parent_id': '440000'},
            {'id': '440900', 'name': '茂名市', 'parent_id': '440000'},
            {'id': '441200', 'name': '肇庆市', 'parent_id': '440000'},
            {'id': '441300', 'name': '惠州市', 'parent_id': '440000'},
            {'id': '441400', 'name': '梅州市', 'parent_id': '440000'},
            {'id': '441500', 'name': '汕尾市', 'parent_id': '440000'},
            {'id': '441600', 'name': '河源市', 'parent_id': '440000'},
            {'id': '441700', 'name': '阳江市', 'parent_id': '440000'},
            {'id': '441800', 'name': '清远市', 'parent_id': '440000'},
            {'id': '441900', 'name': '东莞市', 'parent_id': '440000'},
            {'id': '442000', 'name': '中山市', 'parent_id': '440000'},
            {'id': '445100', 'name': '潮州市', 'parent_id': '440000'},
            {'id': '445200', 'name': '揭阳市', 'parent_id': '440000'},
            {'id': '445300', 'name': '云浮市', 'parent_id': '440000'},
        ]
        
        try:
            # 导入省份数据
            print("正在导入省份数据...")
            for idx, province in enumerate(provinces):
                region = Region(
                    id=province['id'],
                    name=province['name'],
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功导入 {len(provinces)} 个省份")
            
            # 导入城市数据
            print("正在导入城市数据...")
            for idx, city in enumerate(cities):
                region = Region(
                    id=city['id'],
                    name=city['name'],
                    parent_id=city['parent_id'],
                    level=2,
                    sort_order=idx
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功导入 {len(cities)} 个城市")

            # 主要城市的区县数据
            districts = [
                # 北京市
                {'id': '110101', 'name': '东城区', 'parent_id': '110100'},
                {'id': '110102', 'name': '西城区', 'parent_id': '110100'},
                {'id': '110105', 'name': '朝阳区', 'parent_id': '110100'},
                {'id': '110106', 'name': '丰台区', 'parent_id': '110100'},
                {'id': '110107', 'name': '石景山区', 'parent_id': '110100'},
                {'id': '110108', 'name': '海淀区', 'parent_id': '110100'},
                {'id': '110109', 'name': '门头沟区', 'parent_id': '110100'},
                {'id': '110111', 'name': '房山区', 'parent_id': '110100'},
                {'id': '110112', 'name': '通州区', 'parent_id': '110100'},
                {'id': '110113', 'name': '顺义区', 'parent_id': '110100'},
                {'id': '110114', 'name': '昌平区', 'parent_id': '110100'},
                {'id': '110115', 'name': '大兴区', 'parent_id': '110100'},
                {'id': '110116', 'name': '怀柔区', 'parent_id': '110100'},
                {'id': '110117', 'name': '平谷区', 'parent_id': '110100'},
                {'id': '110118', 'name': '密云区', 'parent_id': '110100'},
                {'id': '110119', 'name': '延庆区', 'parent_id': '110100'},

                # 天津市
                {'id': '120101', 'name': '和平区', 'parent_id': '120100'},
                {'id': '120102', 'name': '河东区', 'parent_id': '120100'},
                {'id': '120103', 'name': '河西区', 'parent_id': '120100'},
                {'id': '120104', 'name': '南开区', 'parent_id': '120100'},
                {'id': '120105', 'name': '河北区', 'parent_id': '120100'},
                {'id': '120106', 'name': '红桥区', 'parent_id': '120100'},
                {'id': '120110', 'name': '东丽区', 'parent_id': '120100'},
                {'id': '120111', 'name': '西青区', 'parent_id': '120100'},
                {'id': '120112', 'name': '津南区', 'parent_id': '120100'},
                {'id': '120113', 'name': '北辰区', 'parent_id': '120100'},
                {'id': '120114', 'name': '武清区', 'parent_id': '120100'},
                {'id': '120115', 'name': '宝坻区', 'parent_id': '120100'},
                {'id': '120116', 'name': '滨海新区', 'parent_id': '120100'},
                {'id': '120117', 'name': '宁河区', 'parent_id': '120100'},
                {'id': '120118', 'name': '静海区', 'parent_id': '120100'},
                {'id': '120119', 'name': '蓟州区', 'parent_id': '120100'},

                # 上海市
                {'id': '310101', 'name': '黄浦区', 'parent_id': '310100'},
                {'id': '310104', 'name': '徐汇区', 'parent_id': '310100'},
                {'id': '310105', 'name': '长宁区', 'parent_id': '310100'},
                {'id': '310106', 'name': '静安区', 'parent_id': '310100'},
                {'id': '310107', 'name': '普陀区', 'parent_id': '310100'},
                {'id': '310109', 'name': '虹口区', 'parent_id': '310100'},
                {'id': '310110', 'name': '杨浦区', 'parent_id': '310100'},
                {'id': '310112', 'name': '闵行区', 'parent_id': '310100'},
                {'id': '310113', 'name': '宝山区', 'parent_id': '310100'},
                {'id': '310114', 'name': '嘉定区', 'parent_id': '310100'},
                {'id': '310115', 'name': '浦东新区', 'parent_id': '310100'},
                {'id': '310116', 'name': '金山区', 'parent_id': '310100'},
                {'id': '310117', 'name': '松江区', 'parent_id': '310100'},
                {'id': '310118', 'name': '青浦区', 'parent_id': '310100'},
                {'id': '310120', 'name': '奉贤区', 'parent_id': '310100'},
                {'id': '310151', 'name': '崇明区', 'parent_id': '310100'},

                # 重庆市
                {'id': '500101', 'name': '万州区', 'parent_id': '500000'},
                {'id': '500102', 'name': '涪陵区', 'parent_id': '500000'},
                {'id': '500103', 'name': '渝中区', 'parent_id': '500000'},
                {'id': '500104', 'name': '大渡口区', 'parent_id': '500000'},
                {'id': '500105', 'name': '江北区', 'parent_id': '500000'},
                {'id': '500106', 'name': '沙坪坝区', 'parent_id': '500000'},
                {'id': '500107', 'name': '九龙坡区', 'parent_id': '500000'},
                {'id': '500108', 'name': '南岸区', 'parent_id': '500000'},
                {'id': '500109', 'name': '北碚区', 'parent_id': '500000'},
                {'id': '500110', 'name': '綦江区', 'parent_id': '500000'},
                {'id': '500111', 'name': '大足区', 'parent_id': '500000'},
                {'id': '500112', 'name': '渝北区', 'parent_id': '500000'},
                {'id': '500113', 'name': '巴南区', 'parent_id': '500000'},
                {'id': '500114', 'name': '黔江区', 'parent_id': '500000'},
                {'id': '500115', 'name': '长寿区', 'parent_id': '500000'},
                {'id': '500116', 'name': '江津区', 'parent_id': '500000'},
                {'id': '500117', 'name': '合川区', 'parent_id': '500000'},
                {'id': '500118', 'name': '永川区', 'parent_id': '500000'},
                {'id': '500119', 'name': '南川区', 'parent_id': '500000'},
                {'id': '500120', 'name': '璧山区', 'parent_id': '500000'},
                {'id': '500151', 'name': '铜梁区', 'parent_id': '500000'},
                {'id': '500152', 'name': '潼南区', 'parent_id': '500000'},
                {'id': '500153', 'name': '荣昌区', 'parent_id': '500000'},
                {'id': '500154', 'name': '开州区', 'parent_id': '500000'},
                {'id': '500155', 'name': '梁平区', 'parent_id': '500000'},
                {'id': '500156', 'name': '武隆区', 'parent_id': '500000'},

                # 广州市
                {'id': '440103', 'name': '荔湾区', 'parent_id': '440100'},
                {'id': '440104', 'name': '越秀区', 'parent_id': '440100'},
                {'id': '440105', 'name': '海珠区', 'parent_id': '440100'},
                {'id': '440106', 'name': '天河区', 'parent_id': '440100'},
                {'id': '440111', 'name': '白云区', 'parent_id': '440100'},
                {'id': '440112', 'name': '黄埔区', 'parent_id': '440100'},
                {'id': '440113', 'name': '番禺区', 'parent_id': '440100'},
                {'id': '440114', 'name': '花都区', 'parent_id': '440100'},
                {'id': '440115', 'name': '南沙区', 'parent_id': '440100'},
                {'id': '440117', 'name': '从化区', 'parent_id': '440100'},
                {'id': '440118', 'name': '增城区', 'parent_id': '440100'},

                # 深圳市
                {'id': '440303', 'name': '罗湖区', 'parent_id': '440300'},
                {'id': '440304', 'name': '福田区', 'parent_id': '440300'},
                {'id': '440305', 'name': '南山区', 'parent_id': '440300'},
                {'id': '440306', 'name': '宝安区', 'parent_id': '440300'},
                {'id': '440307', 'name': '龙岗区', 'parent_id': '440300'},
                {'id': '440308', 'name': '盐田区', 'parent_id': '440300'},
                {'id': '440309', 'name': '龙华区', 'parent_id': '440300'},
                {'id': '440310', 'name': '坪山区', 'parent_id': '440300'},
                {'id': '440311', 'name': '光明区', 'parent_id': '440300'},

                # 郑州市
                {'id': '410102', 'name': '中原区', 'parent_id': '410100'},
                {'id': '410103', 'name': '二七区', 'parent_id': '410100'},
                {'id': '410104', 'name': '管城回族区', 'parent_id': '410100'},
                {'id': '410105', 'name': '金水区', 'parent_id': '410100'},
                {'id': '410106', 'name': '上街区', 'parent_id': '410100'},
                {'id': '410108', 'name': '惠济区', 'parent_id': '410100'},
                {'id': '410122', 'name': '中牟县', 'parent_id': '410100'},
                {'id': '410181', 'name': '巩义市', 'parent_id': '410100'},
                {'id': '410182', 'name': '荥阳市', 'parent_id': '410100'},
                {'id': '410183', 'name': '新密市', 'parent_id': '410100'},
                {'id': '410184', 'name': '新郑市', 'parent_id': '410100'},
                {'id': '410185', 'name': '登封市', 'parent_id': '410100'},

                # 武汉市
                {'id': '420102', 'name': '江岸区', 'parent_id': '420100'},
                {'id': '420103', 'name': '江汉区', 'parent_id': '420100'},
                {'id': '420104', 'name': '硚口区', 'parent_id': '420100'},
                {'id': '420105', 'name': '汉阳区', 'parent_id': '420100'},
                {'id': '420106', 'name': '武昌区', 'parent_id': '420100'},
                {'id': '420107', 'name': '青山区', 'parent_id': '420100'},
                {'id': '420111', 'name': '洪山区', 'parent_id': '420100'},
                {'id': '420112', 'name': '东西湖区', 'parent_id': '420100'},
                {'id': '420113', 'name': '汉南区', 'parent_id': '420100'},
                {'id': '420114', 'name': '蔡甸区', 'parent_id': '420100'},
                {'id': '420115', 'name': '江夏区', 'parent_id': '420100'},
                {'id': '420116', 'name': '黄陂区', 'parent_id': '420100'},
                {'id': '420117', 'name': '新洲区', 'parent_id': '420100'},
            ]

            # 导入区县数据
            print("正在导入区县数据...")
            for idx, district in enumerate(districts):
                region = Region(
                    id=district['id'],
                    name=district['name'],
                    parent_id=district['parent_id'],
                    level=3,
                    sort_order=idx
                )
                db.session.add(region)

            db.session.commit()
            print(f"成功导入 {len(districts)} 个区县")

            print(f"全国区域数据导入完成！")
            print(f"总计：{len(provinces)} 个省份，{len(cities)} 个城市，{len(districts)} 个区县")
            
        except Exception as e:
            db.session.rollback()
            print(f"导入数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    import_full_regions()
