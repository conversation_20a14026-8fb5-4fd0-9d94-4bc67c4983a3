#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从chengshi文件夹的JSON文件导入全国城市数据
"""

import os
import sys
import json
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

class JSONRegionImporter:
    def __init__(self):
        self.app = create_app()
        
        # 省份ID映射表（标准行政区划代码）
        self.province_id_map = {
            '北京市': '110000',
            '天津市': '120000',
            '河北省': '130000',
            '山西省': '140000',
            '内蒙古自治区': '150000',
            '辽宁省': '210000',
            '吉林省': '220000',
            '黑龙江省': '230000',
            '上海市': '310000',
            '江苏省': '320000',
            '浙江省': '330000',
            '安徽省': '340000',
            '福建省': '350000',
            '江西省': '360000',
            '山东省': '370000',
            '河南省': '410000',
            '湖北省': '420000',
            '湖南省': '430000',
            '广东省': '440000',
            '广西壮族自治区': '450000',
            '海南省': '460000',
            '重庆市': '500000',
            '四川省': '510000',
            '贵州省': '520000',
            '云南省': '530000',
            '西藏自治区': '540000',
            '陕西省': '610000',
            '甘肃省': '620000',
            '青海省': '630000',
            '宁夏回族自治区': '640000',
            '新疆维吾尔自治区': '650000',
            '台湾省': '710000',
            '香港': '810000',
            '澳门': '820000'
        }
    
    def clear_all_data(self):
        """清空所有区域数据"""
        with self.app.app_context():
            print("正在清空所有区域数据...")
            Region.query.delete()
            db.session.commit()
            print("数据已清空")
    
    def load_json_data(self):
        """加载JSON数据"""
        json_file = 'chengshi/liang1024_citys.json'
        if not os.path.exists(json_file):
            print(f"错误：找不到文件 {json_file}")
            return None
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载JSON文件：{json_file}")
            return data
        except Exception as e:
            print(f"加载JSON文件失败：{str(e)}")
            return None
    
    def generate_city_id(self, province_id, city_index):
        """生成城市ID"""
        # 对于直辖市，使用特殊规则
        if province_id in ['110000', '120000', '310000', '500000']:
            return province_id[:-2] + '100'  # 110100, 120100, 310100, 500100
        
        # 对于其他省份，按顺序生成
        base_code = province_id[:-4]  # 取前两位
        city_code = f"{base_code}{(city_index + 1):02d}00"
        return city_code
    
    def import_provinces(self, provinces_data):
        """导入省份数据"""
        with self.app.app_context():
            print("正在导入省份数据...")
            imported_count = 0
            
            for idx, province_data in enumerate(provinces_data):
                province_name = province_data['provinceName']
                province_id = self.province_id_map.get(province_name)
                
                if not province_id:
                    print(f"警告：未找到省份 {province_name} 的ID映射，跳过")
                    continue
                
                # 检查是否已存在
                existing = Region.query.filter_by(id=province_id).first()
                if existing:
                    print(f"省份 {province_name} 已存在，跳过")
                    continue
                
                region = Region(
                    id=province_id,
                    name=province_name,
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
                imported_count += 1
            
            db.session.commit()
            print(f"成功导入 {imported_count} 个省份")
    
    def import_cities(self, provinces_data):
        """导入城市数据"""
        with self.app.app_context():
            print("正在导入城市数据...")
            total_cities = 0
            
            for province_data in provinces_data:
                province_name = province_data['provinceName']
                province_id = self.province_id_map.get(province_name)
                
                if not province_id:
                    continue
                
                cities = province_data.get('citys', [])
                print(f"正在导入 {province_name} 的 {len(cities)} 个城市...")
                
                imported_count = 0
                for city_index, city_data in enumerate(cities):
                    city_name = city_data['cityName']
                    city_id = self.generate_city_id(province_id, city_index)
                    
                    # 检查是否已存在
                    existing = Region.query.filter_by(id=city_id).first()
                    if existing:
                        continue
                    
                    region = Region(
                        id=city_id,
                        name=city_name,
                        parent_id=province_id,
                        level=2,
                        sort_order=city_index
                    )
                    db.session.add(region)
                    imported_count += 1
                
                if imported_count > 0:
                    db.session.commit()
                    print(f"✅ {province_name}: 成功导入 {imported_count} 个城市")
                    total_cities += imported_count
                else:
                    print(f"⚠️ {province_name}: 所有城市已存在")
            
            print(f"总计导入 {total_cities} 个城市")
    
    def run_import(self):
        """执行完整导入"""
        print("=== 开始从JSON文件导入全国区域数据 ===")
        
        # 加载JSON数据
        data = self.load_json_data()
        if not data:
            return
        
        provinces_data = data.get('provinces', [])
        print(f"发现 {len(provinces_data)} 个省份数据")
        
        # 清空现有数据
        self.clear_all_data()
        
        # 导入省份
        self.import_provinces(provinces_data)
        
        # 导入城市
        self.import_cities(provinces_data)
        
        # 最终统计
        with self.app.app_context():
            provinces = Region.query.filter_by(level=1).count()
            cities = Region.query.filter_by(level=2).count()
            districts = Region.query.filter_by(level=3).count()
            
            print(f"\n=== 导入完成 ===")
            print(f"省份数量: {provinces}")
            print(f"城市数量: {cities}")
            print(f"区县数量: {districts}")
            print(f"总计: {provinces + cities + districts} 条记录")
            
            # 显示部分省份的城市数量
            print(f"\n=== 部分省份城市统计 ===")
            sample_provinces = Region.query.filter_by(level=1).limit(10).all()
            for p in sample_provinces:
                city_count = Region.query.filter_by(parent_id=p.id, level=2).count()
                print(f"{p.name}: {city_count}个城市")

if __name__ == '__main__':
    importer = JSONRegionImporter()
    importer.run_import()
