"""创建区域表

Revision ID: a7b5c9d8e3f2
Revises: 
Create Date: 2023-06-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a7b5c9d8e3f2'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 创建区域表
    op.create_table(
        'regions',
        sa.Column('id', sa.String(20), primary_key=True),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('parent_id', sa.String(20), nullable=True, index=True),
        sa.Column('level', sa.Integer, nullable=False),
        sa.Column('sort_order', sa.Integer, default=0),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # 添加索引
    op.create_index('ix_regions_level', 'regions', ['level'])
    op.create_index('ix_regions_is_active', 'regions', ['is_active'])


def downgrade():
    # 删除索引
    op.drop_index('ix_regions_is_active', table_name='regions')
    op.drop_index('ix_regions_level', table_name='regions')
    
    # 删除表
    op.drop_table('regions') 