#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
按省份逐个导入全国区域数据
确保每个省份的城市和区县数据完整
"""

import os
import sys
import time
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

class ProvinceByProvinceImporter:
    def __init__(self):
        self.app = create_app()
        
    def clear_all_data(self):
        """清空所有区域数据"""
        with self.app.app_context():
            print("正在清空所有区域数据...")
            Region.query.delete()
            db.session.commit()
            print("数据已清空")
    
    def import_provinces(self):
        """导入所有省份"""
        provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '130000', 'name': '河北省'},
            {'id': '140000', 'name': '山西省'},
            {'id': '150000', 'name': '内蒙古自治区'},
            {'id': '210000', 'name': '辽宁省'},
            {'id': '220000', 'name': '吉林省'},
            {'id': '230000', 'name': '黑龙江省'},
            {'id': '310000', 'name': '上海市'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
            {'id': '340000', 'name': '安徽省'},
            {'id': '350000', 'name': '福建省'},
            {'id': '360000', 'name': '江西省'},
            {'id': '370000', 'name': '山东省'},
            {'id': '410000', 'name': '河南省'},
            {'id': '420000', 'name': '湖北省'},
            {'id': '430000', 'name': '湖南省'},
            {'id': '440000', 'name': '广东省'},
            {'id': '450000', 'name': '广西壮族自治区'},
            {'id': '460000', 'name': '海南省'},
            {'id': '500000', 'name': '重庆市'},
            {'id': '510000', 'name': '四川省'},
            {'id': '520000', 'name': '贵州省'},
            {'id': '530000', 'name': '云南省'},
            {'id': '540000', 'name': '西藏自治区'},
            {'id': '610000', 'name': '陕西省'},
            {'id': '620000', 'name': '甘肃省'},
            {'id': '630000', 'name': '青海省'},
            {'id': '640000', 'name': '宁夏回族自治区'},
            {'id': '650000', 'name': '新疆维吾尔自治区'},
            {'id': '710000', 'name': '台湾省'},
            {'id': '810000', 'name': '香港特别行政区'},
            {'id': '820000', 'name': '澳门特别行政区'}
        ]
        
        with self.app.app_context():
            print("正在导入省份数据...")
            for idx, province in enumerate(provinces):
                region = Region(
                    id=province['id'],
                    name=province['name'],
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功导入 {len(provinces)} 个省份")
    
    def get_province_data(self, province_id):
        """获取指定省份的城市和区县数据"""
        province_data = {
            '110000': {  # 北京市
                'cities': [
                    {'id': '110100', 'name': '北京市'}
                ],
                'districts': {
                    '110100': [
                        {'id': '110101', 'name': '东城区'},
                        {'id': '110102', 'name': '西城区'},
                        {'id': '110105', 'name': '朝阳区'},
                        {'id': '110106', 'name': '丰台区'},
                        {'id': '110107', 'name': '石景山区'},
                        {'id': '110108', 'name': '海淀区'},
                        {'id': '110109', 'name': '门头沟区'},
                        {'id': '110111', 'name': '房山区'},
                        {'id': '110112', 'name': '通州区'},
                        {'id': '110113', 'name': '顺义区'},
                        {'id': '110114', 'name': '昌平区'},
                        {'id': '110115', 'name': '大兴区'},
                        {'id': '110116', 'name': '怀柔区'},
                        {'id': '110117', 'name': '平谷区'},
                        {'id': '110118', 'name': '密云区'},
                        {'id': '110119', 'name': '延庆区'},
                    ]
                }
            },
            '120000': {  # 天津市
                'cities': [
                    {'id': '120100', 'name': '天津市'}
                ],
                'districts': {
                    '120100': [
                        {'id': '120101', 'name': '和平区'},
                        {'id': '120102', 'name': '河东区'},
                        {'id': '120103', 'name': '河西区'},
                        {'id': '120104', 'name': '南开区'},
                        {'id': '120105', 'name': '河北区'},
                        {'id': '120106', 'name': '红桥区'},
                        {'id': '120110', 'name': '东丽区'},
                        {'id': '120111', 'name': '西青区'},
                        {'id': '120112', 'name': '津南区'},
                        {'id': '120113', 'name': '北辰区'},
                        {'id': '120114', 'name': '武清区'},
                        {'id': '120115', 'name': '宝坻区'},
                        {'id': '120116', 'name': '滨海新区'},
                        {'id': '120117', 'name': '宁河区'},
                        {'id': '120118', 'name': '静海区'},
                        {'id': '120119', 'name': '蓟州区'},
                    ]
                }
            },
            '310000': {  # 上海市
                'cities': [
                    {'id': '310100', 'name': '上海市'}
                ],
                'districts': {
                    '310100': [
                        {'id': '310101', 'name': '黄浦区'},
                        {'id': '310104', 'name': '徐汇区'},
                        {'id': '310105', 'name': '长宁区'},
                        {'id': '310106', 'name': '静安区'},
                        {'id': '310107', 'name': '普陀区'},
                        {'id': '310109', 'name': '虹口区'},
                        {'id': '310110', 'name': '杨浦区'},
                        {'id': '310112', 'name': '闵行区'},
                        {'id': '310113', 'name': '宝山区'},
                        {'id': '310114', 'name': '嘉定区'},
                        {'id': '310115', 'name': '浦东新区'},
                        {'id': '310116', 'name': '金山区'},
                        {'id': '310117', 'name': '松江区'},
                        {'id': '310118', 'name': '青浦区'},
                        {'id': '310120', 'name': '奉贤区'},
                        {'id': '310151', 'name': '崇明区'},
                    ]
                }
            },
            '320000': {  # 江苏省
                'cities': [
                    {'id': '320100', 'name': '南京市'},
                    {'id': '320200', 'name': '无锡市'},
                    {'id': '320300', 'name': '徐州市'},
                    {'id': '320400', 'name': '常州市'},
                    {'id': '320500', 'name': '苏州市'},
                    {'id': '320600', 'name': '南通市'},
                    {'id': '320700', 'name': '连云港市'},
                    {'id': '320800', 'name': '淮安市'},
                    {'id': '320900', 'name': '盐城市'},
                    {'id': '321000', 'name': '扬州市'},
                    {'id': '321100', 'name': '镇江市'},
                    {'id': '321200', 'name': '泰州市'},
                    {'id': '321300', 'name': '宿迁市'},
                ],
                'districts': {
                    '320100': [  # 南京市
                        {'id': '320102', 'name': '玄武区'},
                        {'id': '320104', 'name': '秦淮区'},
                        {'id': '320105', 'name': '建邺区'},
                        {'id': '320106', 'name': '鼓楼区'},
                        {'id': '320111', 'name': '浦口区'},
                        {'id': '320113', 'name': '栖霞区'},
                        {'id': '320114', 'name': '雨花台区'},
                        {'id': '320115', 'name': '江宁区'},
                        {'id': '320116', 'name': '六合区'},
                        {'id': '320117', 'name': '溧水区'},
                        {'id': '320118', 'name': '高淳区'},
                    ]
                }
            },
            '440000': {  # 广东省
                'cities': [
                    {'id': '440100', 'name': '广州市'},
                    {'id': '440200', 'name': '韶关市'},
                    {'id': '440300', 'name': '深圳市'},
                    {'id': '440400', 'name': '珠海市'},
                    {'id': '440500', 'name': '汕头市'},
                    {'id': '440600', 'name': '佛山市'},
                    {'id': '440700', 'name': '江门市'},
                    {'id': '440800', 'name': '湛江市'},
                    {'id': '440900', 'name': '茂名市'},
                    {'id': '441200', 'name': '肇庆市'},
                    {'id': '441300', 'name': '惠州市'},
                    {'id': '441400', 'name': '梅州市'},
                    {'id': '441500', 'name': '汕尾市'},
                    {'id': '441600', 'name': '河源市'},
                    {'id': '441700', 'name': '阳江市'},
                    {'id': '441800', 'name': '清远市'},
                    {'id': '441900', 'name': '东莞市'},
                    {'id': '442000', 'name': '中山市'},
                    {'id': '445100', 'name': '潮州市'},
                    {'id': '445200', 'name': '揭阳市'},
                    {'id': '445300', 'name': '云浮市'},
                ],
                'districts': {
                    '440100': [  # 广州市
                        {'id': '440103', 'name': '荔湾区'},
                        {'id': '440104', 'name': '越秀区'},
                        {'id': '440105', 'name': '海珠区'},
                        {'id': '440106', 'name': '天河区'},
                        {'id': '440111', 'name': '白云区'},
                        {'id': '440112', 'name': '黄埔区'},
                        {'id': '440113', 'name': '番禺区'},
                        {'id': '440114', 'name': '花都区'},
                        {'id': '440115', 'name': '南沙区'},
                        {'id': '440117', 'name': '从化区'},
                        {'id': '440118', 'name': '增城区'},
                    ],
                    '440300': [  # 深圳市
                        {'id': '440303', 'name': '罗湖区'},
                        {'id': '440304', 'name': '福田区'},
                        {'id': '440305', 'name': '南山区'},
                        {'id': '440306', 'name': '宝安区'},
                        {'id': '440307', 'name': '龙岗区'},
                        {'id': '440308', 'name': '盐田区'},
                        {'id': '440309', 'name': '龙华区'},
                        {'id': '440310', 'name': '坪山区'},
                        {'id': '440311', 'name': '光明区'},
                    ]
                }
            },
            '330000': {  # 浙江省
                'cities': [
                    {'id': '330100', 'name': '杭州市'},
                    {'id': '330200', 'name': '宁波市'},
                    {'id': '330300', 'name': '温州市'},
                    {'id': '330400', 'name': '嘉兴市'},
                    {'id': '330500', 'name': '湖州市'},
                    {'id': '330600', 'name': '绍兴市'},
                    {'id': '330700', 'name': '金华市'},
                    {'id': '330800', 'name': '衢州市'},
                    {'id': '330900', 'name': '舟山市'},
                    {'id': '331000', 'name': '台州市'},
                    {'id': '331100', 'name': '丽水市'},
                ],
                'districts': {
                    '330100': [  # 杭州市
                        {'id': '330102', 'name': '上城区'},
                        {'id': '330103', 'name': '下城区'},
                        {'id': '330104', 'name': '江干区'},
                        {'id': '330105', 'name': '拱墅区'},
                        {'id': '330106', 'name': '西湖区'},
                        {'id': '330108', 'name': '滨江区'},
                        {'id': '330109', 'name': '萧山区'},
                        {'id': '330110', 'name': '余杭区'},
                        {'id': '330111', 'name': '富阳区'},
                        {'id': '330112', 'name': '临安区'},
                        {'id': '330122', 'name': '桐庐县'},
                        {'id': '330127', 'name': '淳安县'},
                        {'id': '330182', 'name': '建德市'},
                    ]
                }
            },
            '410000': {  # 河南省
                'cities': [
                    {'id': '410100', 'name': '郑州市'},
                    {'id': '410200', 'name': '开封市'},
                    {'id': '410300', 'name': '洛阳市'},
                    {'id': '410400', 'name': '平顶山市'},
                    {'id': '410500', 'name': '安阳市'},
                    {'id': '410600', 'name': '鹤壁市'},
                    {'id': '410700', 'name': '新乡市'},
                    {'id': '410800', 'name': '焦作市'},
                    {'id': '410900', 'name': '濮阳市'},
                    {'id': '411000', 'name': '许昌市'},
                    {'id': '411100', 'name': '漯河市'},
                    {'id': '411200', 'name': '三门峡市'},
                    {'id': '411300', 'name': '南阳市'},
                    {'id': '411400', 'name': '商丘市'},
                    {'id': '411500', 'name': '信阳市'},
                    {'id': '411600', 'name': '周口市'},
                    {'id': '411700', 'name': '驻马店市'},
                    {'id': '419001', 'name': '济源市'},
                ],
                'districts': {
                    '410100': [  # 郑州市
                        {'id': '410102', 'name': '中原区'},
                        {'id': '410103', 'name': '二七区'},
                        {'id': '410104', 'name': '管城回族区'},
                        {'id': '410105', 'name': '金水区'},
                        {'id': '410106', 'name': '上街区'},
                        {'id': '410108', 'name': '惠济区'},
                        {'id': '410122', 'name': '中牟县'},
                        {'id': '410181', 'name': '巩义市'},
                        {'id': '410182', 'name': '荥阳市'},
                        {'id': '410183', 'name': '新密市'},
                        {'id': '410184', 'name': '新郑市'},
                        {'id': '410185', 'name': '登封市'},
                    ]
                }
            },
            '420000': {  # 湖北省
                'cities': [
                    {'id': '420100', 'name': '武汉市'},
                    {'id': '420200', 'name': '黄石市'},
                    {'id': '420300', 'name': '十堰市'},
                    {'id': '420500', 'name': '宜昌市'},
                    {'id': '420600', 'name': '襄阳市'},
                    {'id': '420700', 'name': '鄂州市'},
                    {'id': '420800', 'name': '荆门市'},
                    {'id': '420900', 'name': '孝感市'},
                    {'id': '421000', 'name': '荆州市'},
                    {'id': '421100', 'name': '黄冈市'},
                    {'id': '421200', 'name': '咸宁市'},
                    {'id': '421300', 'name': '随州市'},
                    {'id': '422800', 'name': '恩施土家族苗族自治州'},
                ],
                'districts': {
                    '420100': [  # 武汉市
                        {'id': '420102', 'name': '江岸区'},
                        {'id': '420103', 'name': '江汉区'},
                        {'id': '420104', 'name': '硚口区'},
                        {'id': '420105', 'name': '汉阳区'},
                        {'id': '420106', 'name': '武昌区'},
                        {'id': '420107', 'name': '青山区'},
                        {'id': '420111', 'name': '洪山区'},
                        {'id': '420112', 'name': '东西湖区'},
                        {'id': '420113', 'name': '汉南区'},
                        {'id': '420114', 'name': '蔡甸区'},
                        {'id': '420115', 'name': '江夏区'},
                        {'id': '420116', 'name': '黄陂区'},
                        {'id': '420117', 'name': '新洲区'},
                    ]
                }
            },
            '510000': {  # 四川省
                'cities': [
                    {'id': '510100', 'name': '成都市'},
                    {'id': '510300', 'name': '自贡市'},
                    {'id': '510400', 'name': '攀枝花市'},
                    {'id': '510500', 'name': '泸州市'},
                    {'id': '510600', 'name': '德阳市'},
                    {'id': '510700', 'name': '绵阳市'},
                    {'id': '510800', 'name': '广元市'},
                    {'id': '510900', 'name': '遂宁市'},
                    {'id': '511000', 'name': '内江市'},
                    {'id': '511100', 'name': '乐山市'},
                    {'id': '511300', 'name': '南充市'},
                    {'id': '511400', 'name': '眉山市'},
                    {'id': '511500', 'name': '宜宾市'},
                    {'id': '511600', 'name': '广安市'},
                    {'id': '511700', 'name': '达州市'},
                    {'id': '511800', 'name': '雅安市'},
                    {'id': '511900', 'name': '巴中市'},
                    {'id': '512000', 'name': '资阳市'},
                    {'id': '513200', 'name': '阿坝藏族羌族自治州'},
                    {'id': '513300', 'name': '甘孜藏族自治州'},
                    {'id': '513400', 'name': '凉山彝族自治州'},
                ],
                'districts': {
                    '510100': [  # 成都市
                        {'id': '510104', 'name': '锦江区'},
                        {'id': '510105', 'name': '青羊区'},
                        {'id': '510106', 'name': '金牛区'},
                        {'id': '510107', 'name': '武侯区'},
                        {'id': '510108', 'name': '成华区'},
                        {'id': '510112', 'name': '龙泉驿区'},
                        {'id': '510113', 'name': '青白江区'},
                        {'id': '510114', 'name': '新都区'},
                        {'id': '510115', 'name': '温江区'},
                        {'id': '510116', 'name': '双流区'},
                        {'id': '510117', 'name': '郫都区'},
                        {'id': '510118', 'name': '新津区'},
                        {'id': '510121', 'name': '金堂县'},
                        {'id': '510129', 'name': '大邑县'},
                        {'id': '510131', 'name': '蒲江县'},
                        {'id': '510181', 'name': '都江堰市'},
                        {'id': '510182', 'name': '彭州市'},
                        {'id': '510183', 'name': '邛崃市'},
                        {'id': '510184', 'name': '崇州市'},
                        {'id': '510185', 'name': '简阳市'},
                    ]
                }
            }
        }

        return province_data.get(province_id, {'cities': [], 'districts': {}})
    
    def import_province_data(self, province_id, province_name):
        """导入指定省份的数据"""
        print(f"\n=== 开始导入 {province_name} ===")
        
        data = self.get_province_data(province_id)
        cities = data['cities']
        districts_data = data['districts']
        
        with self.app.app_context():
            try:
                # 导入城市
                if cities:
                    print(f"导入 {len(cities)} 个城市...")
                    for idx, city in enumerate(cities):
                        region = Region(
                            id=city['id'],
                            name=city['name'],
                            parent_id=province_id,
                            level=2,
                            sort_order=idx
                        )
                        db.session.add(region)
                    
                    db.session.commit()
                    print(f"✅ 成功导入 {len(cities)} 个城市")
                
                # 导入区县
                total_districts = 0
                for city_id, districts in districts_data.items():
                    if districts:
                        print(f"导入 {city_id} 的 {len(districts)} 个区县...")
                        for idx, district in enumerate(districts):
                            region = Region(
                                id=district['id'],
                                name=district['name'],
                                parent_id=city_id,
                                level=3,
                                sort_order=idx
                            )
                            db.session.add(region)
                        total_districts += len(districts)
                
                if total_districts > 0:
                    db.session.commit()
                    print(f"✅ 成功导入 {total_districts} 个区县")
                
                print(f"✅ {province_name} 导入完成")
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ {province_name} 导入失败: {str(e)}")
    
    def run_import(self):
        """执行按省份导入"""
        print("=== 开始按省份逐个导入全国区域数据 ===")
        
        # 清空数据
        self.clear_all_data()
        
        # 导入省份
        self.import_provinces()
        
        # 按省份导入数据（分批进行）
        provinces_to_import = [
            ('110000', '北京市'),
            ('120000', '天津市'),
            ('310000', '上海市'),
            ('320000', '江苏省'),
            ('330000', '浙江省'),
            ('440000', '广东省'),
            ('410000', '河南省'),
            ('420000', '湖北省'),
            ('510000', '四川省'),
        ]
        
        for province_id, province_name in provinces_to_import:
            self.import_province_data(province_id, province_name)
            time.sleep(0.5)  # 短暂休息
        
        # 最终统计
        with self.app.app_context():
            provinces = Region.query.filter_by(level=1).count()
            cities = Region.query.filter_by(level=2).count()
            districts = Region.query.filter_by(level=3).count()
            
            print(f"\n=== 导入完成 ===")
            print(f"省份数量: {provinces}")
            print(f"城市数量: {cities}")
            print(f"区县数量: {districts}")
            print(f"总计: {provinces + cities + districts} 条记录")

if __name__ == '__main__':
    importer = ProvinceByProvinceImporter()
    importer.run_import()
