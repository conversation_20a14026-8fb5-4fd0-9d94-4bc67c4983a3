#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全国区域数据最终统计
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models.system import Region

def show_final_summary():
    """显示最终的数据统计"""
    app = create_app()
    with app.app_context():
        print("🎉 全国区域数据导入完成！")
        print("=" * 60)
        
        # 总体统计
        provinces = Region.query.filter_by(level=1).count()
        cities = Region.query.filter_by(level=2).count()
        districts = Region.query.filter_by(level=3).count()
        total = provinces + cities + districts
        
        print(f"📊 数据统计总览")
        print(f"   省份数量: {provinces:,} 个")
        print(f"   城市数量: {cities:,} 个")
        print(f"   区县数量: {districts:,} 个")
        print(f"   总计记录: {total:,} 条")
        print()
        
        # 各省份城市统计
        print(f"🏙️ 各省份城市分布 (前15个省份)")
        print("-" * 40)
        provinces_list = Region.query.filter_by(level=1).all()
        province_stats = []
        
        for province in provinces_list:
            city_count = Region.query.filter_by(parent_id=province.id, level=2).count()
            # 简化区县统计，直接统计该省份下所有区县
            cities_in_province = Region.query.filter_by(parent_id=province.id, level=2).all()
            district_count = 0
            for city in cities_in_province:
                district_count += Region.query.filter_by(parent_id=city.id, level=3).count()

            province_stats.append({
                'name': province.name,
                'cities': city_count,
                'districts': district_count
            })
        
        # 按城市数量排序
        province_stats.sort(key=lambda x: x['cities'], reverse=True)
        
        for i, stat in enumerate(province_stats[:15], 1):
            print(f"{i:2d}. {stat['name']:<12} {stat['cities']:2d}个城市")
        
        print()
        
        # 主要城市区县统计
        print(f"🏛️ 主要城市区县分布")
        print("-" * 40)
        major_cities = [
            '110100',  # 北京市
            '120100',  # 天津市
            '310100',  # 上海市
            '500000',  # 重庆市（直辖市特殊处理）
            '440100',  # 广州市
            '440300',  # 深圳市
            '320100',  # 南京市
            '330100',  # 杭州市
            '510100',  # 成都市
            '420100',  # 武汉市
        ]
        
        for city_id in major_cities:
            if city_id == '500000':  # 重庆市特殊处理
                city = Region.query.filter_by(id=city_id, level=1).first()
                if city:
                    district_count = Region.query.filter_by(parent_id=city_id, level=2).count()
                    print(f"{city.name:<8} {district_count:2d}个区县")
            else:
                city = Region.query.filter_by(id=city_id, level=2).first()
                if city:
                    district_count = Region.query.filter_by(parent_id=city_id, level=3).count()
                    print(f"{city.name:<8} {district_count:2d}个区县")
        
        print()
        
        # API接口测试
        print(f"🔗 API接口地址")
        print("-" * 40)
        print(f"省份列表: http://127.0.0.1:5000/api/regions/provinces")
        print(f"城市列表: http://127.0.0.1:5000/api/regions/cities/{{province_id}}")
        print(f"区县列表: http://127.0.0.1:5000/api/regions/districts/{{city_id}}")
        print()
        print(f"示例:")
        print(f"  广东省城市: http://127.0.0.1:5000/api/regions/cities/440000")
        print(f"  广州市区县: http://127.0.0.1:5000/api/regions/districts/440100")
        print(f"  北京市区县: http://127.0.0.1:5000/api/regions/districts/110100")
        print()
        
        # 测试页面
        print(f"🌐 测试页面")
        print("-" * 40)
        print(f"简化版联动测试: http://127.0.0.1:5000/simple-region-test")
        print(f"调试页面:       http://127.0.0.1:5000/debug-regions")
        print(f"原始测试页面:   http://127.0.0.1:5000/test-form")
        print()
        
        # 使用说明
        print(f"💡 使用说明")
        print("-" * 40)
        print(f"1. 前端初始化:")
        print(f"   const regionSelector = new SimpleRegionSelector({{")
        print(f"       provinceSelector: '#province',")
        print(f"       citySelector: '#city',")
        print(f"       districtSelector: '#district',")
        print(f"       onChange: function(region) {{")
        print(f"           console.log('选中区域:', region);")
        print(f"       }}")
        print(f"   }});")
        print()
        print(f"2. 获取选中区域:")
        print(f"   const region = regionSelector.getSelectedRegion();")
        print(f"   // region.province.name - 省份名称")
        print(f"   // region.city.name - 城市名称")
        print(f"   // region.district.name - 区县名称")
        print()
        
        print(f"✅ 系统已就绪，可以开始使用全国城市联动功能！")

if __name__ == '__main__':
    show_final_summary()
