#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分批次导入全国完整的省市县数据
支持断点续传，避免一次性导入过多数据
"""

import os
import sys
import json
import time
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

class BatchRegionImporter:
    def __init__(self):
        self.app = create_app()
        self.batch_size = 50  # 每批次导入50条记录
        
    def check_existing_data(self):
        """检查现有数据"""
        with self.app.app_context():
            provinces = Region.query.filter_by(level=1).count()
            cities = Region.query.filter_by(level=2).count()
            districts = Region.query.filter_by(level=3).count()
            
            print(f"=== 当前数据统计 ===")
            print(f"省份数量: {provinces}")
            print(f"城市数量: {cities}")
            print(f"区县数量: {districts}")
            print(f"总计: {provinces + cities + districts} 条记录")
            
            return provinces, cities, districts
    
    def get_missing_provinces(self):
        """获取缺失的省份数据"""
        all_provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '130000', 'name': '河北省'},
            {'id': '140000', 'name': '山西省'},
            {'id': '150000', 'name': '内蒙古自治区'},
            {'id': '210000', 'name': '辽宁省'},
            {'id': '220000', 'name': '吉林省'},
            {'id': '230000', 'name': '黑龙江省'},
            {'id': '310000', 'name': '上海市'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
            {'id': '340000', 'name': '安徽省'},
            {'id': '350000', 'name': '福建省'},
            {'id': '360000', 'name': '江西省'},
            {'id': '370000', 'name': '山东省'},
            {'id': '410000', 'name': '河南省'},
            {'id': '420000', 'name': '湖北省'},
            {'id': '430000', 'name': '湖南省'},
            {'id': '440000', 'name': '广东省'},
            {'id': '450000', 'name': '广西壮族自治区'},
            {'id': '460000', 'name': '海南省'},
            {'id': '500000', 'name': '重庆市'},
            {'id': '510000', 'name': '四川省'},
            {'id': '520000', 'name': '贵州省'},
            {'id': '530000', 'name': '云南省'},
            {'id': '540000', 'name': '西藏自治区'},
            {'id': '610000', 'name': '陕西省'},
            {'id': '620000', 'name': '甘肃省'},
            {'id': '630000', 'name': '青海省'},
            {'id': '640000', 'name': '宁夏回族自治区'},
            {'id': '650000', 'name': '新疆维吾尔自治区'},
            {'id': '710000', 'name': '台湾省'},
            {'id': '810000', 'name': '香港特别行政区'},
            {'id': '820000', 'name': '澳门特别行政区'}
        ]
        
        with self.app.app_context():
            existing_ids = set(r.id for r in Region.query.filter_by(level=1).all())
            missing = [p for p in all_provinces if p['id'] not in existing_ids]
            return missing
    
    def get_missing_cities_for_province(self, province_id):
        """获取指定省份缺失的城市数据"""
        # 这里定义各省份的城市数据
        province_cities = {
            '130000': [  # 河北省
                {'id': '130100', 'name': '石家庄市'},
                {'id': '130200', 'name': '唐山市'},
                {'id': '130300', 'name': '秦皇岛市'},
                {'id': '130400', 'name': '邯郸市'},
                {'id': '130500', 'name': '邢台市'},
                {'id': '130600', 'name': '保定市'},
                {'id': '130700', 'name': '张家口市'},
                {'id': '130800', 'name': '承德市'},
                {'id': '130900', 'name': '沧州市'},
                {'id': '131000', 'name': '廊坊市'},
                {'id': '131100', 'name': '衡水市'},
            ],
            '140000': [  # 山西省
                {'id': '140100', 'name': '太原市'},
                {'id': '140200', 'name': '大同市'},
                {'id': '140300', 'name': '阳泉市'},
                {'id': '140400', 'name': '长治市'},
                {'id': '140500', 'name': '晋城市'},
                {'id': '140600', 'name': '朔州市'},
                {'id': '140700', 'name': '晋中市'},
                {'id': '140800', 'name': '运城市'},
                {'id': '140900', 'name': '忻州市'},
                {'id': '141000', 'name': '临汾市'},
                {'id': '141100', 'name': '吕梁市'},
            ],
            '150000': [  # 内蒙古自治区
                {'id': '150100', 'name': '呼和浩特市'},
                {'id': '150200', 'name': '包头市'},
                {'id': '150300', 'name': '乌海市'},
                {'id': '150400', 'name': '赤峰市'},
                {'id': '150500', 'name': '通辽市'},
                {'id': '150600', 'name': '鄂尔多斯市'},
                {'id': '150700', 'name': '呼伦贝尔市'},
                {'id': '150800', 'name': '巴彦淖尔市'},
                {'id': '150900', 'name': '乌兰察布市'},
                {'id': '152200', 'name': '兴安盟'},
                {'id': '152500', 'name': '锡林郭勒盟'},
                {'id': '152900', 'name': '阿拉善盟'},
            ],
            '210000': [  # 辽宁省
                {'id': '210100', 'name': '沈阳市'},
                {'id': '210200', 'name': '大连市'},
                {'id': '210300', 'name': '鞍山市'},
                {'id': '210400', 'name': '抚顺市'},
                {'id': '210500', 'name': '本溪市'},
                {'id': '210600', 'name': '丹东市'},
                {'id': '210700', 'name': '锦州市'},
                {'id': '210800', 'name': '营口市'},
                {'id': '210900', 'name': '阜新市'},
                {'id': '211000', 'name': '辽阳市'},
                {'id': '211100', 'name': '盘锦市'},
                {'id': '211200', 'name': '铁岭市'},
                {'id': '211300', 'name': '朝阳市'},
                {'id': '211400', 'name': '葫芦岛市'},
            ],
            '220000': [  # 吉林省
                {'id': '220100', 'name': '长春市'},
                {'id': '220200', 'name': '吉林市'},
                {'id': '220300', 'name': '四平市'},
                {'id': '220400', 'name': '辽源市'},
                {'id': '220500', 'name': '通化市'},
                {'id': '220600', 'name': '白山市'},
                {'id': '220700', 'name': '松原市'},
                {'id': '220800', 'name': '白城市'},
                {'id': '222400', 'name': '延边朝鲜族自治州'},
            ],
            '230000': [  # 黑龙江省
                {'id': '230100', 'name': '哈尔滨市'},
                {'id': '230200', 'name': '齐齐哈尔市'},
                {'id': '230300', 'name': '鸡西市'},
                {'id': '230400', 'name': '鹤岗市'},
                {'id': '230500', 'name': '双鸭山市'},
                {'id': '230600', 'name': '大庆市'},
                {'id': '230700', 'name': '伊春市'},
                {'id': '230800', 'name': '佳木斯市'},
                {'id': '230900', 'name': '七台河市'},
                {'id': '231000', 'name': '牡丹江市'},
                {'id': '231100', 'name': '黑河市'},
                {'id': '231200', 'name': '绥化市'},
                {'id': '232700', 'name': '大兴安岭地区'},
            ],
            '320000': [  # 江苏省
                {'id': '320100', 'name': '南京市'},
                {'id': '320200', 'name': '无锡市'},
                {'id': '320300', 'name': '徐州市'},
                {'id': '320400', 'name': '常州市'},
                {'id': '320500', 'name': '苏州市'},
                {'id': '320600', 'name': '南通市'},
                {'id': '320700', 'name': '连云港市'},
                {'id': '320800', 'name': '淮安市'},
                {'id': '320900', 'name': '盐城市'},
                {'id': '321000', 'name': '扬州市'},
                {'id': '321100', 'name': '镇江市'},
                {'id': '321200', 'name': '泰州市'},
                {'id': '321300', 'name': '宿迁市'},
            ],
            '330000': [  # 浙江省
                {'id': '330100', 'name': '杭州市'},
                {'id': '330200', 'name': '宁波市'},
                {'id': '330300', 'name': '温州市'},
                {'id': '330400', 'name': '嘉兴市'},
                {'id': '330500', 'name': '湖州市'},
                {'id': '330600', 'name': '绍兴市'},
                {'id': '330700', 'name': '金华市'},
                {'id': '330800', 'name': '衢州市'},
                {'id': '330900', 'name': '舟山市'},
                {'id': '331000', 'name': '台州市'},
                {'id': '331100', 'name': '丽水市'},
            ],
            '340000': [  # 安徽省
                {'id': '340100', 'name': '合肥市'},
                {'id': '340200', 'name': '芜湖市'},
                {'id': '340300', 'name': '蚌埠市'},
                {'id': '340400', 'name': '淮南市'},
                {'id': '340500', 'name': '马鞍山市'},
                {'id': '340600', 'name': '淮北市'},
                {'id': '340700', 'name': '铜陵市'},
                {'id': '340800', 'name': '安庆市'},
                {'id': '341000', 'name': '黄山市'},
                {'id': '341100', 'name': '滁州市'},
                {'id': '341200', 'name': '阜阳市'},
                {'id': '341300', 'name': '宿州市'},
                {'id': '341500', 'name': '六安市'},
                {'id': '341600', 'name': '亳州市'},
                {'id': '341700', 'name': '池州市'},
                {'id': '341800', 'name': '宣城市'},
            ],
            '350000': [  # 福建省
                {'id': '350100', 'name': '福州市'},
                {'id': '350200', 'name': '厦门市'},
                {'id': '350300', 'name': '莆田市'},
                {'id': '350400', 'name': '三明市'},
                {'id': '350500', 'name': '泉州市'},
                {'id': '350600', 'name': '漳州市'},
                {'id': '350700', 'name': '南平市'},
                {'id': '350800', 'name': '龙岩市'},
                {'id': '350900', 'name': '宁德市'},
            ],
            '360000': [  # 江西省
                {'id': '360100', 'name': '南昌市'},
                {'id': '360200', 'name': '景德镇市'},
                {'id': '360300', 'name': '萍乡市'},
                {'id': '360400', 'name': '九江市'},
                {'id': '360500', 'name': '新余市'},
                {'id': '360600', 'name': '鹰潭市'},
                {'id': '360700', 'name': '赣州市'},
                {'id': '360800', 'name': '吉安市'},
                {'id': '360900', 'name': '宜春市'},
                {'id': '361000', 'name': '抚州市'},
                {'id': '361100', 'name': '上饶市'},
            ],
            '370000': [  # 山东省
                {'id': '370100', 'name': '济南市'},
                {'id': '370200', 'name': '青岛市'},
                {'id': '370300', 'name': '淄博市'},
                {'id': '370400', 'name': '枣庄市'},
                {'id': '370500', 'name': '东营市'},
                {'id': '370600', 'name': '烟台市'},
                {'id': '370700', 'name': '潍坊市'},
                {'id': '370800', 'name': '济宁市'},
                {'id': '370900', 'name': '泰安市'},
                {'id': '371000', 'name': '威海市'},
                {'id': '371100', 'name': '日照市'},
                {'id': '371300', 'name': '临沂市'},
                {'id': '371400', 'name': '德州市'},
                {'id': '371500', 'name': '聊城市'},
                {'id': '371600', 'name': '滨州市'},
                {'id': '371700', 'name': '菏泽市'},
            ],
            '450000': [  # 广西壮族自治区
                {'id': '450100', 'name': '南宁市'},
                {'id': '450200', 'name': '柳州市'},
                {'id': '450300', 'name': '桂林市'},
                {'id': '450400', 'name': '梧州市'},
                {'id': '450500', 'name': '北海市'},
                {'id': '450600', 'name': '防城港市'},
                {'id': '450700', 'name': '钦州市'},
                {'id': '450800', 'name': '贵港市'},
                {'id': '450900', 'name': '玉林市'},
                {'id': '451000', 'name': '百色市'},
                {'id': '451100', 'name': '贺州市'},
                {'id': '451200', 'name': '河池市'},
                {'id': '451300', 'name': '来宾市'},
                {'id': '451400', 'name': '崇左市'},
            ],
            '460000': [  # 海南省
                {'id': '460100', 'name': '海口市'},
                {'id': '460200', 'name': '三亚市'},
                {'id': '460300', 'name': '三沙市'},
                {'id': '460400', 'name': '儋州市'},
            ],
            '510000': [  # 四川省
                {'id': '510100', 'name': '成都市'},
                {'id': '510300', 'name': '自贡市'},
                {'id': '510400', 'name': '攀枝花市'},
                {'id': '510500', 'name': '泸州市'},
                {'id': '510600', 'name': '德阳市'},
                {'id': '510700', 'name': '绵阳市'},
                {'id': '510800', 'name': '广元市'},
                {'id': '510900', 'name': '遂宁市'},
                {'id': '511000', 'name': '内江市'},
                {'id': '511100', 'name': '乐山市'},
                {'id': '511300', 'name': '南充市'},
                {'id': '511400', 'name': '眉山市'},
                {'id': '511500', 'name': '宜宾市'},
                {'id': '511600', 'name': '广安市'},
                {'id': '511700', 'name': '达州市'},
                {'id': '511800', 'name': '雅安市'},
                {'id': '511900', 'name': '巴中市'},
                {'id': '512000', 'name': '资阳市'},
                {'id': '513200', 'name': '阿坝藏族羌族自治州'},
                {'id': '513300', 'name': '甘孜藏族自治州'},
                {'id': '513400', 'name': '凉山彝族自治州'},
            ],
        }
        
        cities = province_cities.get(province_id, [])
        
        with self.app.app_context():
            existing_ids = set(r.id for r in Region.query.filter_by(parent_id=province_id, level=2).all())
            missing = [c for c in cities if c['id'] not in existing_ids]
            return missing
    
    def import_batch(self, data_list, level, parent_id='0'):
        """批量导入数据"""
        with self.app.app_context():
            try:
                for i in range(0, len(data_list), self.batch_size):
                    batch = data_list[i:i + self.batch_size]
                    
                    for idx, item in enumerate(batch):
                        region = Region(
                            id=item['id'],
                            name=item['name'],
                            parent_id=parent_id if level == 1 else item.get('parent_id', parent_id),
                            level=level,
                            sort_order=i + idx
                        )
                        db.session.add(region)
                    
                    db.session.commit()
                    print(f"已导入 {min(i + self.batch_size, len(data_list))}/{len(data_list)} 条记录")
                    time.sleep(0.1)  # 短暂休息，避免数据库压力过大
                
                return True
            except Exception as e:
                db.session.rollback()
                print(f"导入失败: {str(e)}")
                return False
    
    def run_import(self):
        """执行分批导入"""
        print("=== 开始分批导入全国区域数据 ===")
        
        # 检查现有数据
        self.check_existing_data()
        
        # 1. 导入缺失的省份
        missing_provinces = self.get_missing_provinces()
        if missing_provinces:
            print(f"\n发现 {len(missing_provinces)} 个缺失的省份，开始导入...")
            if self.import_batch(missing_provinces, level=1):
                print("省份数据导入完成")
            else:
                print("省份数据导入失败")
                return
        else:
            print("\n所有省份数据已存在")
        
        # 2. 导入缺失的城市（分省份处理）
        print("\n开始检查和导入城市数据...")
        with self.app.app_context():
            provinces = Region.query.filter_by(level=1).all()
            
            for province in provinces:
                missing_cities = self.get_missing_cities_for_province(province.id)
                if missing_cities:
                    print(f"\n{province.name} 缺失 {len(missing_cities)} 个城市，开始导入...")
                    # 为城市数据添加parent_id
                    for city in missing_cities:
                        city['parent_id'] = province.id
                    
                    if self.import_batch(missing_cities, level=2):
                        print(f"{province.name} 城市数据导入完成")
                    else:
                        print(f"{province.name} 城市数据导入失败")
                else:
                    print(f"{province.name} 城市数据已完整")
        
        # 最终统计
        print("\n=== 导入完成，最终统计 ===")
        self.check_existing_data()

if __name__ == '__main__':
    importer = BatchRegionImporter()
    importer.run_import()
