#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从chengshi文件夹的详细JSON文件导入完整的省市区县数据
包含区县级别的完整数据
"""

import os
import sys
import json
import glob
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

class CompleteRegionImporter:
    def __init__(self):
        self.app = create_app()
        
        # 省份文件映射
        self.province_files = {
            'chengshi/GD2025.json': '440000',  # 广东省
            'chengshi/JS2025.json': '320000',  # 江苏省
            'chengshi/ZJ2025.json': '330000',  # 浙江省
            'chengshi/SH2025.json': '310000',  # 上海市
            'chengshi/hunan2025.json': '430000',  # 湖南省
        }
    
    def clear_all_data(self):
        """清空所有区域数据"""
        with self.app.app_context():
            print("正在清空所有区域数据...")
            Region.query.delete()
            db.session.commit()
            print("数据已清空")
    
    def load_json_file(self, file_path):
        """加载单个JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载文件：{file_path}")
            return data
        except Exception as e:
            print(f"加载文件失败 {file_path}: {str(e)}")
            return None
    
    def import_provinces_from_main_json(self):
        """从主JSON文件导入所有省份"""
        main_file = 'chengshi/liang1024_citys.json'
        data = self.load_json_file(main_file)
        if not data:
            return
        
        # 省份ID映射表
        province_id_map = {
            '北京市': '110000',
            '天津市': '120000',
            '河北省': '130000',
            '山西省': '140000',
            '内蒙古自治区': '150000',
            '辽宁省': '210000',
            '吉林省': '220000',
            '黑龙江省': '230000',
            '上海市': '310000',
            '江苏省': '320000',
            '浙江省': '330000',
            '安徽省': '340000',
            '福建省': '350000',
            '江西省': '360000',
            '山东省': '370000',
            '河南省': '410000',
            '湖北省': '420000',
            '湖南省': '430000',
            '广东省': '440000',
            '广西壮族自治区': '450000',
            '海南省': '460000',
            '重庆市': '500000',
            '四川省': '510000',
            '贵州省': '520000',
            '云南省': '530000',
            '西藏自治区': '540000',
            '陕西省': '610000',
            '甘肃省': '620000',
            '青海省': '630000',
            '宁夏回族自治区': '640000',
            '新疆维吾尔自治区': '650000',
            '台湾省': '710000',
            '香港': '810000',
            '澳门': '820000'
        }
        
        with self.app.app_context():
            print("正在导入省份数据...")
            provinces_data = data.get('provinces', [])
            imported_count = 0
            
            for idx, province_data in enumerate(provinces_data):
                province_name = province_data['provinceName']
                province_id = province_id_map.get(province_name)
                
                if not province_id:
                    print(f"警告：未找到省份 {province_name} 的ID映射，跳过")
                    continue
                
                region = Region(
                    id=province_id,
                    name=province_name,
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
                imported_count += 1
            
            db.session.commit()
            print(f"成功导入 {imported_count} 个省份")
    
    def import_province_details(self, file_path, province_id):
        """导入单个省份的详细数据"""
        data = self.load_json_file(file_path)
        if not data:
            return
        
        province_name = data.get('name', '未知省份')
        print(f"\n=== 开始导入 {province_name} 的详细数据 ===")
        
        with self.app.app_context():
            cities_data = data.get('citys', [])
            
            # 导入城市
            print(f"导入 {len(cities_data)} 个城市...")
            city_count = 0
            district_count = 0
            
            for city_idx, city_data in enumerate(cities_data):
                city_name = city_data.get('name', '')
                city_code = city_data.get('code', '')
                
                # 提取城市ID（去掉后面的000）
                city_id = city_code[:6] if len(city_code) >= 6 else city_code
                
                # 检查城市是否已存在
                existing_city = Region.query.filter_by(id=city_id).first()
                if not existing_city:
                    city_region = Region(
                        id=city_id,
                        name=city_name,
                        parent_id=province_id,
                        level=2,
                        sort_order=city_idx
                    )
                    db.session.add(city_region)
                    city_count += 1
                
                # 导入区县
                areas_data = city_data.get('areas', [])
                if areas_data:
                    print(f"  {city_name}: 导入 {len(areas_data)} 个区县...")
                    
                    for area_idx, area_data in enumerate(areas_data):
                        area_name = area_data.get('name', '')
                        area_code = area_data.get('code', '')
                        
                        # 提取区县ID
                        area_id = area_code[:6] if len(area_code) >= 6 else area_code
                        
                        # 检查区县是否已存在
                        existing_area = Region.query.filter_by(id=area_id).first()
                        if not existing_area:
                            area_region = Region(
                                id=area_id,
                                name=area_name,
                                parent_id=city_id,
                                level=3,
                                sort_order=area_idx
                            )
                            db.session.add(area_region)
                            district_count += 1
            
            # 提交数据
            if city_count > 0 or district_count > 0:
                db.session.commit()
                print(f"✅ {province_name}: 成功导入 {city_count} 个城市，{district_count} 个区县")
            else:
                print(f"⚠️ {province_name}: 所有数据已存在")
    
    def run_import(self):
        """执行完整导入"""
        print("=== 开始导入完整的全国区域数据 ===")
        
        # 清空现有数据
        self.clear_all_data()
        
        # 导入所有省份
        self.import_provinces_from_main_json()
        
        # 导入详细的省份数据
        for file_path, province_id in self.province_files.items():
            if os.path.exists(file_path):
                self.import_province_details(file_path, province_id)
            else:
                print(f"文件不存在：{file_path}")
        
        # 从主JSON文件导入其他省份的城市数据
        self.import_remaining_cities()
        
        # 最终统计
        with self.app.app_context():
            provinces = Region.query.filter_by(level=1).count()
            cities = Region.query.filter_by(level=2).count()
            districts = Region.query.filter_by(level=3).count()
            
            print(f"\n=== 导入完成 ===")
            print(f"省份数量: {provinces}")
            print(f"城市数量: {cities}")
            print(f"区县数量: {districts}")
            print(f"总计: {provinces + cities + districts} 条记录")
            
            # 显示有区县数据的省份
            print(f"\n=== 有完整区县数据的省份 ===")
            for file_path, province_id in self.province_files.items():
                province = Region.query.filter_by(id=province_id).first()
                if province:
                    city_count = Region.query.filter_by(parent_id=province_id, level=2).count()
                    district_count = 0
                    cities = Region.query.filter_by(parent_id=province_id, level=2).all()
                    for city in cities:
                        district_count += Region.query.filter_by(parent_id=city.id, level=3).count()
                    print(f"{province.name}: {city_count}个城市, {district_count}个区县")
    
    def import_remaining_cities(self):
        """从主JSON文件导入其他省份的城市数据"""
        print(f"\n=== 导入其他省份的城市数据 ===")
        
        main_file = 'chengshi/liang1024_citys.json'
        data = self.load_json_file(main_file)
        if not data:
            return
        
        # 省份ID映射表
        province_id_map = {
            '北京市': '110000',
            '天津市': '120000',
            '河北省': '130000',
            '山西省': '140000',
            '内蒙古自治区': '150000',
            '辽宁省': '210000',
            '吉林省': '220000',
            '黑龙江省': '230000',
            '上海市': '310000',
            '江苏省': '320000',
            '浙江省': '330000',
            '安徽省': '340000',
            '福建省': '350000',
            '江西省': '360000',
            '山东省': '370000',
            '河南省': '410000',
            '湖北省': '420000',
            '湖南省': '430000',
            '广东省': '440000',
            '广西壮族自治区': '450000',
            '海南省': '460000',
            '重庆市': '500000',
            '四川省': '510000',
            '贵州省': '520000',
            '云南省': '530000',
            '西藏自治区': '540000',
            '陕西省': '610000',
            '甘肃省': '620000',
            '青海省': '630000',
            '宁夏回族自治区': '640000',
            '新疆维吾尔自治区': '650000',
            '台湾省': '710000',
            '香港': '810000',
            '澳门': '820000'
        }
        
        # 已有详细数据的省份，跳过
        detailed_provinces = set(self.province_files.values())
        
        with self.app.app_context():
            provinces_data = data.get('provinces', [])
            
            for province_data in provinces_data:
                province_name = province_data['provinceName']
                province_id = province_id_map.get(province_name)
                
                if not province_id or province_id in detailed_provinces:
                    continue
                
                cities = province_data.get('citys', [])
                if not cities:
                    continue
                
                print(f"导入 {province_name} 的 {len(cities)} 个城市...")
                
                imported_count = 0
                for city_index, city_data in enumerate(cities):
                    city_name = city_data['cityName']
                    
                    # 生成城市ID
                    if province_id in ['110000', '120000', '310000', '500000']:
                        city_id = province_id[:-2] + '100'
                    else:
                        base_code = province_id[:-4]
                        city_code = f"{base_code}{(city_index + 1):02d}00"
                        city_id = city_code
                    
                    # 检查是否已存在
                    existing = Region.query.filter_by(id=city_id).first()
                    if existing:
                        continue
                    
                    region = Region(
                        id=city_id,
                        name=city_name,
                        parent_id=province_id,
                        level=2,
                        sort_order=city_index
                    )
                    db.session.add(region)
                    imported_count += 1
                
                if imported_count > 0:
                    db.session.commit()
                    print(f"✅ {province_name}: 成功导入 {imported_count} 个城市")

if __name__ == '__main__':
    importer = CompleteRegionImporter()
    importer.run_import()
