#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加测试区域数据
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

def add_test_regions():
    """添加测试区域数据"""
    app = create_app()
    with app.app_context():
        # 检查是否已有数据
        existing_count = Region.query.count()
        if existing_count > 0:
            print(f"数据库中已存在 {existing_count} 条区域数据")
            return
        
        # 添加测试省份数据
        provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '130000', 'name': '河北省'},
            {'id': '140000', 'name': '山西省'},
            {'id': '150000', 'name': '内蒙古自治区'},
            {'id': '210000', 'name': '辽宁省'},
            {'id': '220000', 'name': '吉林省'},
            {'id': '230000', 'name': '黑龙江省'},
            {'id': '310000', 'name': '上海市'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
            {'id': '340000', 'name': '安徽省'},
            {'id': '350000', 'name': '福建省'},
            {'id': '360000', 'name': '江西省'},
            {'id': '370000', 'name': '山东省'},
            {'id': '410000', 'name': '河南省'},
            {'id': '420000', 'name': '湖北省'},
            {'id': '430000', 'name': '湖南省'},
            {'id': '440000', 'name': '广东省'},
            {'id': '450000', 'name': '广西壮族自治区'},
            {'id': '460000', 'name': '海南省'},
            {'id': '500000', 'name': '重庆市'},
            {'id': '510000', 'name': '四川省'},
            {'id': '520000', 'name': '贵州省'},
            {'id': '530000', 'name': '云南省'},
            {'id': '540000', 'name': '西藏自治区'},
            {'id': '610000', 'name': '陕西省'},
            {'id': '620000', 'name': '甘肃省'},
            {'id': '630000', 'name': '青海省'},
            {'id': '640000', 'name': '宁夏回族自治区'},
            {'id': '650000', 'name': '新疆维吾尔自治区'},
            {'id': '710000', 'name': '台湾省'},
            {'id': '810000', 'name': '香港特别行政区'},
            {'id': '820000', 'name': '澳门特别行政区'}
        ]
        
        # 添加测试城市数据（以广东省为例）
        cities = [
            {'id': '440100', 'name': '广州市', 'parent_id': '440000'},
            {'id': '440200', 'name': '韶关市', 'parent_id': '440000'},
            {'id': '440300', 'name': '深圳市', 'parent_id': '440000'},
            {'id': '440400', 'name': '珠海市', 'parent_id': '440000'},
            {'id': '440500', 'name': '汕头市', 'parent_id': '440000'},
            {'id': '440600', 'name': '佛山市', 'parent_id': '440000'},
            {'id': '440700', 'name': '江门市', 'parent_id': '440000'},
            {'id': '440800', 'name': '湛江市', 'parent_id': '440000'},
            {'id': '440900', 'name': '茂名市', 'parent_id': '440000'},
            {'id': '441200', 'name': '肇庆市', 'parent_id': '440000'},
            {'id': '441300', 'name': '惠州市', 'parent_id': '440000'},
            {'id': '441400', 'name': '梅州市', 'parent_id': '440000'},
            {'id': '441500', 'name': '汕尾市', 'parent_id': '440000'},
            {'id': '441600', 'name': '河源市', 'parent_id': '440000'},
            {'id': '441700', 'name': '阳江市', 'parent_id': '440000'},
            {'id': '441800', 'name': '清远市', 'parent_id': '440000'},
            {'id': '441900', 'name': '东莞市', 'parent_id': '440000'},
            {'id': '442000', 'name': '中山市', 'parent_id': '440000'},
            {'id': '445100', 'name': '潮州市', 'parent_id': '440000'},
            {'id': '445200', 'name': '揭阳市', 'parent_id': '440000'},
            {'id': '445300', 'name': '云浮市', 'parent_id': '440000'}
        ]
        
        # 添加测试区县数据（以广州市为例）
        districts = [
            {'id': '440103', 'name': '荔湾区', 'parent_id': '440100'},
            {'id': '440104', 'name': '越秀区', 'parent_id': '440100'},
            {'id': '440105', 'name': '海珠区', 'parent_id': '440100'},
            {'id': '440106', 'name': '天河区', 'parent_id': '440100'},
            {'id': '440111', 'name': '白云区', 'parent_id': '440100'},
            {'id': '440112', 'name': '黄埔区', 'parent_id': '440100'},
            {'id': '440113', 'name': '番禺区', 'parent_id': '440100'},
            {'id': '440114', 'name': '花都区', 'parent_id': '440100'},
            {'id': '440115', 'name': '南沙区', 'parent_id': '440100'},
            {'id': '440117', 'name': '从化区', 'parent_id': '440100'},
            {'id': '440118', 'name': '增城区', 'parent_id': '440100'}
        ]
        
        try:
            # 添加省份
            for idx, province in enumerate(provinces):
                region = Region(
                    id=province['id'],
                    name=province['name'],
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
            
            # 添加城市
            for idx, city in enumerate(cities):
                region = Region(
                    id=city['id'],
                    name=city['name'],
                    parent_id=city['parent_id'],
                    level=2,
                    sort_order=idx
                )
                db.session.add(region)
            
            # 添加区县
            for idx, district in enumerate(districts):
                region = Region(
                    id=district['id'],
                    name=district['name'],
                    parent_id=district['parent_id'],
                    level=3,
                    sort_order=idx
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功添加测试区域数据：{len(provinces)} 个省份，{len(cities)} 个城市，{len(districts)} 个区县")
            
        except Exception as e:
            db.session.rollback()
            print(f"添加数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_test_regions()
