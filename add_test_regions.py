#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加测试区域数据
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

def add_test_regions():
    """添加测试区域数据"""
    app = create_app()
    with app.app_context():
        # 清空现有数据
        Region.query.delete()
        db.session.commit()
        print("已清空现有区域数据")
        
        # 添加测试省份数据
        provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '130000', 'name': '河北省'},
            {'id': '140000', 'name': '山西省'},
            {'id': '150000', 'name': '内蒙古自治区'},
            {'id': '210000', 'name': '辽宁省'},
            {'id': '220000', 'name': '吉林省'},
            {'id': '230000', 'name': '黑龙江省'},
            {'id': '310000', 'name': '上海市'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
            {'id': '340000', 'name': '安徽省'},
            {'id': '350000', 'name': '福建省'},
            {'id': '360000', 'name': '江西省'},
            {'id': '370000', 'name': '山东省'},
            {'id': '410000', 'name': '河南省'},
            {'id': '420000', 'name': '湖北省'},
            {'id': '430000', 'name': '湖南省'},
            {'id': '440000', 'name': '广东省'},
            {'id': '450000', 'name': '广西壮族自治区'},
            {'id': '460000', 'name': '海南省'},
            {'id': '500000', 'name': '重庆市'},
            {'id': '510000', 'name': '四川省'},
            {'id': '520000', 'name': '贵州省'},
            {'id': '530000', 'name': '云南省'},
            {'id': '540000', 'name': '西藏自治区'},
            {'id': '610000', 'name': '陕西省'},
            {'id': '620000', 'name': '甘肃省'},
            {'id': '630000', 'name': '青海省'},
            {'id': '640000', 'name': '宁夏回族自治区'},
            {'id': '650000', 'name': '新疆维吾尔自治区'},
            {'id': '710000', 'name': '台湾省'},
            {'id': '810000', 'name': '香港特别行政区'},
            {'id': '820000', 'name': '澳门特别行政区'}
        ]
        
        # 添加测试城市数据（多个省份）
        cities = [
            # 广东省
            {'id': '440100', 'name': '广州市', 'parent_id': '440000'},
            {'id': '440200', 'name': '韶关市', 'parent_id': '440000'},
            {'id': '440300', 'name': '深圳市', 'parent_id': '440000'},
            {'id': '440400', 'name': '珠海市', 'parent_id': '440000'},
            {'id': '440500', 'name': '汕头市', 'parent_id': '440000'},
            {'id': '440600', 'name': '佛山市', 'parent_id': '440000'},
            {'id': '440700', 'name': '江门市', 'parent_id': '440000'},
            {'id': '440800', 'name': '湛江市', 'parent_id': '440000'},
            {'id': '440900', 'name': '茂名市', 'parent_id': '440000'},
            {'id': '441200', 'name': '肇庆市', 'parent_id': '440000'},
            {'id': '441300', 'name': '惠州市', 'parent_id': '440000'},
            {'id': '441400', 'name': '梅州市', 'parent_id': '440000'},
            {'id': '441500', 'name': '汕尾市', 'parent_id': '440000'},
            {'id': '441600', 'name': '河源市', 'parent_id': '440000'},
            {'id': '441700', 'name': '阳江市', 'parent_id': '440000'},
            {'id': '441800', 'name': '清远市', 'parent_id': '440000'},
            {'id': '441900', 'name': '东莞市', 'parent_id': '440000'},
            {'id': '442000', 'name': '中山市', 'parent_id': '440000'},
            {'id': '445100', 'name': '潮州市', 'parent_id': '440000'},
            {'id': '445200', 'name': '揭阳市', 'parent_id': '440000'},
            {'id': '445300', 'name': '云浮市', 'parent_id': '440000'},
            # 北京市
            {'id': '110100', 'name': '北京市', 'parent_id': '110000'},
            # 上海市
            {'id': '310100', 'name': '上海市', 'parent_id': '310000'},
            # 河南省
            {'id': '410100', 'name': '郑州市', 'parent_id': '410000'},
            {'id': '410200', 'name': '开封市', 'parent_id': '410000'},
            {'id': '410300', 'name': '洛阳市', 'parent_id': '410000'},
            {'id': '410400', 'name': '平顶山市', 'parent_id': '410000'},
            {'id': '410500', 'name': '安阳市', 'parent_id': '410000'}
        ]
        
        # 添加测试区县数据（多个城市）
        districts = [
            # 广州市
            {'id': '440103', 'name': '荔湾区', 'parent_id': '440100'},
            {'id': '440104', 'name': '越秀区', 'parent_id': '440100'},
            {'id': '440105', 'name': '海珠区', 'parent_id': '440100'},
            {'id': '440106', 'name': '天河区', 'parent_id': '440100'},
            {'id': '440111', 'name': '白云区', 'parent_id': '440100'},
            {'id': '440112', 'name': '黄埔区', 'parent_id': '440100'},
            {'id': '440113', 'name': '番禺区', 'parent_id': '440100'},
            {'id': '440114', 'name': '花都区', 'parent_id': '440100'},
            {'id': '440115', 'name': '南沙区', 'parent_id': '440100'},
            {'id': '440117', 'name': '从化区', 'parent_id': '440100'},
            {'id': '440118', 'name': '增城区', 'parent_id': '440100'},
            # 深圳市
            {'id': '440303', 'name': '罗湖区', 'parent_id': '440300'},
            {'id': '440304', 'name': '福田区', 'parent_id': '440300'},
            {'id': '440305', 'name': '南山区', 'parent_id': '440300'},
            {'id': '440306', 'name': '宝安区', 'parent_id': '440300'},
            {'id': '440307', 'name': '龙岗区', 'parent_id': '440300'},
            {'id': '440308', 'name': '盐田区', 'parent_id': '440300'},
            # 北京市
            {'id': '110101', 'name': '东城区', 'parent_id': '110100'},
            {'id': '110102', 'name': '西城区', 'parent_id': '110100'},
            {'id': '110105', 'name': '朝阳区', 'parent_id': '110100'},
            {'id': '110106', 'name': '丰台区', 'parent_id': '110100'},
            {'id': '110107', 'name': '石景山区', 'parent_id': '110100'},
            {'id': '110108', 'name': '海淀区', 'parent_id': '110100'},
            # 上海市
            {'id': '310101', 'name': '黄浦区', 'parent_id': '310100'},
            {'id': '310104', 'name': '徐汇区', 'parent_id': '310100'},
            {'id': '310105', 'name': '长宁区', 'parent_id': '310100'},
            {'id': '310106', 'name': '静安区', 'parent_id': '310100'},
            {'id': '310107', 'name': '普陀区', 'parent_id': '310100'},
            {'id': '310115', 'name': '浦东新区', 'parent_id': '310100'},
            # 郑州市
            {'id': '410102', 'name': '中原区', 'parent_id': '410100'},
            {'id': '410103', 'name': '二七区', 'parent_id': '410100'},
            {'id': '410104', 'name': '管城回族区', 'parent_id': '410100'},
            {'id': '410105', 'name': '金水区', 'parent_id': '410100'},
            {'id': '410106', 'name': '上街区', 'parent_id': '410100'}
        ]
        
        try:
            # 添加省份
            for idx, province in enumerate(provinces):
                region = Region(
                    id=province['id'],
                    name=province['name'],
                    parent_id='0',
                    level=1,
                    sort_order=idx
                )
                db.session.add(region)
            
            # 添加城市
            for idx, city in enumerate(cities):
                region = Region(
                    id=city['id'],
                    name=city['name'],
                    parent_id=city['parent_id'],
                    level=2,
                    sort_order=idx
                )
                db.session.add(region)
            
            # 添加区县
            for idx, district in enumerate(districts):
                region = Region(
                    id=district['id'],
                    name=district['name'],
                    parent_id=district['parent_id'],
                    level=3,
                    sort_order=idx
                )
                db.session.add(region)
            
            db.session.commit()
            print(f"成功添加测试区域数据：{len(provinces)} 个省份，{len(cities)} 个城市，{len(districts)} 个区县")
            
        except Exception as e:
            db.session.rollback()
            print(f"添加数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_test_regions()
