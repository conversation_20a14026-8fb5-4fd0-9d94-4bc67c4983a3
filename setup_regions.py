#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置区域数据的命令行脚本
执行数据库迁移和导入区域数据
"""

import os
import sys
import subprocess
from flask_migrate import Migrate, upgrade
from app import create_app
from models import db
from utils.import_regions import import_regions

def setup_regions():
    """设置区域数据"""
    print("开始设置区域数据...")
    
    # 创建应用实例
    app = create_app()
    
    with app.app_context():
        # 初始化迁移
        migrate = Migrate(app, db)
        
        # 执行数据库迁移
        print("执行数据库迁移...")
        upgrade()
        
        # 导入区域数据
        print("导入区域数据...")
        import_regions()
        
        print("区域数据设置完成！")

if __name__ == '__main__':
    setup_regions() 