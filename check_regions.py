#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查区域数据
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models.system import Region

def check_regions():
    """检查区域数据"""
    app = create_app()
    with app.app_context():
        # 检查数据库中的数据
        provinces = Region.query.filter_by(level=1).all()
        print(f'省份数据: {len(provinces)}个')
        for p in provinces[:5]:
            print(f'  {p.id}: {p.name}')
        
        # 检查广东省的城市数据
        cities = Region.query.filter_by(parent_id='440000', level=2).all()
        print(f'\n广东省城市数据 (parent_id=440000): {len(cities)}个')
        for c in cities:
            print(f'  {c.id}: {c.name}')
        
        # 检查所有level=2的数据
        all_cities = Region.query.filter_by(level=2).all()
        print(f'\n所有城市数据: {len(all_cities)}个')
        for c in all_cities:
            print(f'  {c.id}: {c.name}, parent_id: {c.parent_id}')
        
        # 检查广州市的区县数据
        districts = Region.query.filter_by(parent_id='440100', level=3).all()
        print(f'\n广州市区县数据 (parent_id=440100): {len(districts)}个')
        for d in districts:
            print(f'  {d.id}: {d.name}')

if __name__ == '__main__':
    check_regions()
