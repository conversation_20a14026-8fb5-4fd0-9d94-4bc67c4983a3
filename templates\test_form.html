<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/region-selector.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .form-control {
            color: #333333 !important;
            font-weight: 500;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
            background: white;
        }
        
        .form-control option {
            color: #333333 !important;
            font-weight: 500;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .required-mark {
            color: #dc3545;
        }
        
        .form-control.loading {
            background-image: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            background-size: 200% 100%;
            animation: loading-shimmer 1.5s infinite;
            opacity: 0.7;
        }
        
        @keyframes loading-shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .region-selector-error {
            color: #dc3545;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 4px;
            border-left: 3px solid #dc3545;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="text-center mb-4">表单文本颜色和城市联动测试</h2>
        
        <form>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="testName" class="form-label">姓名 <span class="required-mark">*</span></label>
                    <input type="text" class="form-control" id="testName" placeholder="请输入姓名" value="测试用户">
                </div>
                <div class="col-md-6">
                    <label for="testPhone" class="form-label">电话 <span class="required-mark">*</span></label>
                    <input type="tel" class="form-control" id="testPhone" placeholder="请输入电话" value="13800138000">
                </div>
                <div class="col-md-4">
                    <label for="testProvince" class="form-label">省份 <span class="required-mark">*</span></label>
                    <select class="form-control" id="testProvince">
                        <option value="">请选择省份</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="testCity" class="form-label">城市 <span class="required-mark">*</span></label>
                    <select class="form-control" id="testCity">
                        <option value="">请选择城市</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="testDistrict" class="form-label">区县 <span class="required-mark">*</span></label>
                    <select class="form-control" id="testDistrict">
                        <option value="">请选择区县</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="testAddress" class="form-label">详细地址 <span class="required-mark">*</span></label>
                    <input type="text" class="form-control" id="testAddress" placeholder="请输入详细地址" value="测试街道123号">
                </div>
                <div class="col-md-6">
                    <label for="testPostal" class="form-label">邮政编码</label>
                    <input type="text" class="form-control" id="testPostal" placeholder="请输入邮政编码" value="510000">
                </div>
                <div class="col-12">
                    <label for="testNotes" class="form-label">备注</label>
                    <textarea class="form-control" id="testNotes" rows="3" placeholder="请输入备注信息">这是一个测试备注，用来检查文本颜色是否正常显示。</textarea>
                </div>
                <div class="col-12 text-center">
                    <button type="button" class="btn btn-test" onclick="testForm()">测试提交</button>
                </div>
            </div>
        </form>
        
        <div class="mt-4">
            <h5>测试说明：</h5>
            <ul>
                <li>检查所有输入框的文本颜色是否清晰可见（深色）</li>
                <li>检查下拉框选项的文本颜色是否正常</li>
                <li>测试省市县三级联动功能是否正常工作</li>
                <li>选择省份后，城市下拉框应该自动加载对应的城市</li>
                <li>选择城市后，区县下拉框应该自动加载对应的区县</li>
            </ul>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
    <script>
        let regionSelector;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化简化版区域选择器
            regionSelector = new SimpleRegionSelector({
                provinceSelector: '#testProvince',
                citySelector: '#testCity',
                districtSelector: '#testDistrict',
                provincePlaceholder: '请选择省份',
                cityPlaceholder: '请选择城市',
                districtPlaceholder: '请选择区县',
                onChange: function(region) {
                    console.log('区域选择变化:', region);
                }
            });
        });
        
        function testForm() {
            const formData = {
                name: document.getElementById('testName').value,
                phone: document.getElementById('testPhone').value,
                province: document.getElementById('testProvince').value,
                city: document.getElementById('testCity').value,
                district: document.getElementById('testDistrict').value,
                address: document.getElementById('testAddress').value,
                postal: document.getElementById('testPostal').value,
                notes: document.getElementById('testNotes').value
            };
            
            console.log('表单数据:', formData);
            alert('表单数据已输出到控制台，请按F12查看');
        }
    </script>
</body>
</html>
