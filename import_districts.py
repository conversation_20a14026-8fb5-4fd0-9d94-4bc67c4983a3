#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分批次导入全国区县数据
"""

import os
import sys
import time
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.system import Region

class DistrictImporter:
    def __init__(self):
        self.app = create_app()
        self.batch_size = 30  # 每批次导入30条记录
        
    def get_districts_data(self):
        """获取全国主要城市的区县数据"""
        return {
            # 北京市
            '110100': [
                {'id': '110101', 'name': '东城区'},
                {'id': '110102', 'name': '西城区'},
                {'id': '110105', 'name': '朝阳区'},
                {'id': '110106', 'name': '丰台区'},
                {'id': '110107', 'name': '石景山区'},
                {'id': '110108', 'name': '海淀区'},
                {'id': '110109', 'name': '门头沟区'},
                {'id': '110111', 'name': '房山区'},
                {'id': '110112', 'name': '通州区'},
                {'id': '110113', 'name': '顺义区'},
                {'id': '110114', 'name': '昌平区'},
                {'id': '110115', 'name': '大兴区'},
                {'id': '110116', 'name': '怀柔区'},
                {'id': '110117', 'name': '平谷区'},
                {'id': '110118', 'name': '密云区'},
                {'id': '110119', 'name': '延庆区'},
            ],
            
            # 天津市
            '120100': [
                {'id': '120101', 'name': '和平区'},
                {'id': '120102', 'name': '河东区'},
                {'id': '120103', 'name': '河西区'},
                {'id': '120104', 'name': '南开区'},
                {'id': '120105', 'name': '河北区'},
                {'id': '120106', 'name': '红桥区'},
                {'id': '120110', 'name': '东丽区'},
                {'id': '120111', 'name': '西青区'},
                {'id': '120112', 'name': '津南区'},
                {'id': '120113', 'name': '北辰区'},
                {'id': '120114', 'name': '武清区'},
                {'id': '120115', 'name': '宝坻区'},
                {'id': '120116', 'name': '滨海新区'},
                {'id': '120117', 'name': '宁河区'},
                {'id': '120118', 'name': '静海区'},
                {'id': '120119', 'name': '蓟州区'},
            ],
            
            # 上海市
            '310100': [
                {'id': '310101', 'name': '黄浦区'},
                {'id': '310104', 'name': '徐汇区'},
                {'id': '310105', 'name': '长宁区'},
                {'id': '310106', 'name': '静安区'},
                {'id': '310107', 'name': '普陀区'},
                {'id': '310109', 'name': '虹口区'},
                {'id': '310110', 'name': '杨浦区'},
                {'id': '310112', 'name': '闵行区'},
                {'id': '310113', 'name': '宝山区'},
                {'id': '310114', 'name': '嘉定区'},
                {'id': '310115', 'name': '浦东新区'},
                {'id': '310116', 'name': '金山区'},
                {'id': '310117', 'name': '松江区'},
                {'id': '310118', 'name': '青浦区'},
                {'id': '310120', 'name': '奉贤区'},
                {'id': '310151', 'name': '崇明区'},
            ],
            
            # 石家庄市
            '130100': [
                {'id': '130102', 'name': '长安区'},
                {'id': '130104', 'name': '桥西区'},
                {'id': '130105', 'name': '新华区'},
                {'id': '130107', 'name': '井陉矿区'},
                {'id': '130108', 'name': '裕华区'},
                {'id': '130109', 'name': '藁城区'},
                {'id': '130110', 'name': '鹿泉区'},
                {'id': '130111', 'name': '栾城区'},
                {'id': '130121', 'name': '井陉县'},
                {'id': '130123', 'name': '正定县'},
                {'id': '130125', 'name': '行唐县'},
                {'id': '130126', 'name': '灵寿县'},
                {'id': '130127', 'name': '高邑县'},
                {'id': '130128', 'name': '深泽县'},
                {'id': '130129', 'name': '赞皇县'},
                {'id': '130130', 'name': '无极县'},
                {'id': '130131', 'name': '平山县'},
                {'id': '130132', 'name': '元氏县'},
                {'id': '130133', 'name': '赵县'},
                {'id': '130181', 'name': '辛集市'},
                {'id': '130183', 'name': '晋州市'},
                {'id': '130184', 'name': '新乐市'},
            ],
            
            # 南京市
            '320100': [
                {'id': '320102', 'name': '玄武区'},
                {'id': '320104', 'name': '秦淮区'},
                {'id': '320105', 'name': '建邺区'},
                {'id': '320106', 'name': '鼓楼区'},
                {'id': '320111', 'name': '浦口区'},
                {'id': '320113', 'name': '栖霞区'},
                {'id': '320114', 'name': '雨花台区'},
                {'id': '320115', 'name': '江宁区'},
                {'id': '320116', 'name': '六合区'},
                {'id': '320117', 'name': '溧水区'},
                {'id': '320118', 'name': '高淳区'},
            ],
            
            # 杭州市
            '330100': [
                {'id': '330102', 'name': '上城区'},
                {'id': '330103', 'name': '下城区'},
                {'id': '330104', 'name': '江干区'},
                {'id': '330105', 'name': '拱墅区'},
                {'id': '330106', 'name': '西湖区'},
                {'id': '330108', 'name': '滨江区'},
                {'id': '330109', 'name': '萧山区'},
                {'id': '330110', 'name': '余杭区'},
                {'id': '330111', 'name': '富阳区'},
                {'id': '330112', 'name': '临安区'},
                {'id': '330122', 'name': '桐庐县'},
                {'id': '330127', 'name': '淳安县'},
                {'id': '330182', 'name': '建德市'},
            ],
            
            # 合肥市
            '340100': [
                {'id': '340102', 'name': '瑶海区'},
                {'id': '340103', 'name': '庐阳区'},
                {'id': '340104', 'name': '蜀山区'},
                {'id': '340111', 'name': '包河区'},
                {'id': '340121', 'name': '长丰县'},
                {'id': '340122', 'name': '肥东县'},
                {'id': '340123', 'name': '肥西县'},
                {'id': '340124', 'name': '庐江县'},
                {'id': '340181', 'name': '巢湖市'},
            ],
            
            # 福州市
            '350100': [
                {'id': '350102', 'name': '鼓楼区'},
                {'id': '350103', 'name': '台江区'},
                {'id': '350104', 'name': '仓山区'},
                {'id': '350105', 'name': '马尾区'},
                {'id': '350111', 'name': '晋安区'},
                {'id': '350112', 'name': '长乐区'},
                {'id': '350121', 'name': '闽侯县'},
                {'id': '350122', 'name': '连江县'},
                {'id': '350123', 'name': '罗源县'},
                {'id': '350124', 'name': '闽清县'},
                {'id': '350125', 'name': '永泰县'},
                {'id': '350128', 'name': '平潭县'},
                {'id': '350181', 'name': '福清市'},
            ],
            
            # 南昌市
            '360100': [
                {'id': '360102', 'name': '东湖区'},
                {'id': '360103', 'name': '西湖区'},
                {'id': '360104', 'name': '青云谱区'},
                {'id': '360105', 'name': '湾里区'},
                {'id': '360111', 'name': '青山湖区'},
                {'id': '360112', 'name': '新建区'},
                {'id': '360121', 'name': '南昌县'},
                {'id': '360123', 'name': '安义县'},
                {'id': '360124', 'name': '进贤县'},
            ],
            
            # 济南市
            '370100': [
                {'id': '370102', 'name': '历下区'},
                {'id': '370103', 'name': '市中区'},
                {'id': '370104', 'name': '槐荫区'},
                {'id': '370105', 'name': '天桥区'},
                {'id': '370112', 'name': '历城区'},
                {'id': '370113', 'name': '长清区'},
                {'id': '370114', 'name': '章丘区'},
                {'id': '370115', 'name': '济阳区'},
                {'id': '370116', 'name': '莱芜区'},
                {'id': '370117', 'name': '钢城区'},
                {'id': '370124', 'name': '平阴县'},
                {'id': '370126', 'name': '商河县'},
            ],
            
            # 成都市
            '510100': [
                {'id': '510104', 'name': '锦江区'},
                {'id': '510105', 'name': '青羊区'},
                {'id': '510106', 'name': '金牛区'},
                {'id': '510107', 'name': '武侯区'},
                {'id': '510108', 'name': '成华区'},
                {'id': '510112', 'name': '龙泉驿区'},
                {'id': '510113', 'name': '青白江区'},
                {'id': '510114', 'name': '新都区'},
                {'id': '510115', 'name': '温江区'},
                {'id': '510116', 'name': '双流区'},
                {'id': '510117', 'name': '郫都区'},
                {'id': '510118', 'name': '新津区'},
                {'id': '510121', 'name': '金堂县'},
                {'id': '510129', 'name': '大邑县'},
                {'id': '510131', 'name': '蒲江县'},
                {'id': '510181', 'name': '都江堰市'},
                {'id': '510182', 'name': '彭州市'},
                {'id': '510183', 'name': '邛崃市'},
                {'id': '510184', 'name': '崇州市'},
                {'id': '510185', 'name': '简阳市'},
            ],
        }
    
    def import_districts_for_city(self, city_id, districts):
        """为指定城市导入区县数据"""
        with self.app.app_context():
            try:
                # 检查是否已存在
                existing_ids = set(r.id for r in Region.query.filter_by(parent_id=city_id, level=3).all())
                missing_districts = [d for d in districts if d['id'] not in existing_ids]
                
                if not missing_districts:
                    return True, 0
                
                # 分批导入
                for i in range(0, len(missing_districts), self.batch_size):
                    batch = missing_districts[i:i + self.batch_size]
                    
                    for idx, district in enumerate(batch):
                        region = Region(
                            id=district['id'],
                            name=district['name'],
                            parent_id=city_id,
                            level=3,
                            sort_order=i + idx
                        )
                        db.session.add(region)
                    
                    db.session.commit()
                    print(f"  已导入 {min(i + self.batch_size, len(missing_districts))}/{len(missing_districts)} 个区县")
                    time.sleep(0.1)
                
                return True, len(missing_districts)
            except Exception as e:
                db.session.rollback()
                print(f"  导入失败: {str(e)}")
                return False, 0
    
    def run_import(self):
        """执行区县数据导入"""
        print("=== 开始导入全国区县数据 ===")
        
        districts_data = self.get_districts_data()
        total_imported = 0
        
        for city_id, districts in districts_data.items():
            with self.app.app_context():
                city = Region.query.filter_by(id=city_id, level=2).first()
                if not city:
                    print(f"城市 {city_id} 不存在，跳过")
                    continue
                
                print(f"\n正在处理 {city.name} ({city_id})...")
                success, count = self.import_districts_for_city(city_id, districts)
                
                if success:
                    if count > 0:
                        print(f"  成功导入 {count} 个区县")
                        total_imported += count
                    else:
                        print(f"  区县数据已完整")
                else:
                    print(f"  导入失败")
        
        print(f"\n=== 区县数据导入完成 ===")
        print(f"本次共导入 {total_imported} 个区县")
        
        # 最终统计
        with self.app.app_context():
            total_districts = Region.query.filter_by(level=3).count()
            print(f"数据库中现有 {total_districts} 个区县")

if __name__ == '__main__':
    importer = DistrictImporter()
    importer.run_import()
